package fcmessage

import (
	"context"
	"fmt"
	"os"
	"sync"

	"firebase.google.com/go/v4/messaging"

	firebase "firebase.google.com/go/v4"
	"google.golang.org/api/option"
)

var (
	client *messaging.Client
	once   sync.Once
)

// initializeClient initializes the Firebase messaging client if not already initialized
func initializeClient() error {
	var initError error
	once.Do(func() {
		fmt.Println("initializing firebase client")
		opt := option.WithCredentialsFile(os.Getenv("FCM_PATH"))
		app, err := firebase.NewApp(context.Background(), nil, opt)
		if err != nil {
			initError = fmt.Errorf("error initializing firebase app: %v", err)
			return
		}

		client, err = app.Messaging(context.Background())
		if err != nil {
			initError = fmt.Errorf("error initializing messaging client: %v", err)
			return
		}
		fmt.Println("firebase client initialized")
	})
	return initError
}

func SendMessage(title, body string, data map[string]string, tokens ...string) error {
	if len(tokens) == 0 || len(tokens[0]) == 0 {
		fmt.Println("can not send message with empty token")
		return nil
	}

	if err := initializeClient(); err != nil {
		return err
	}

	fmt.Printf("sending to %v tokens, title: %v, body: %v\n", len(tokens), title, body)

	ctx := context.Background()

	// for _, m := range messages {
	// 	fmt.Printf("message: %v\n", m)
	// 	res, err := client.Send(ctx, m)
	// 	if err != nil {
	// 		fmt.Printf("(util fcm) send message error: %v\n", err)
	// 	}
	// 	fmt.Printf("message sent: %v\n", res)
	// }

	// resp, err := client.Send(ctx, &messaging.Message{
	// 	Notification: &messaging.Notification{
	// 		Title: title,
	// 		Body:  body,
	// 	},
	// 	Data:  data,
	// 	Token: tokens[0],
	// })

	// fmt.Printf("message sent: %v\n", resp)
	// fmt.Println(err)

	//defnine multicast message
	m := &messaging.MulticastMessage{
		Notification: &messaging.Notification{
			Title: title,
			Body:  body,
		},
		Data:   data,
		Tokens: tokens,
	}

	br, err := client.SendEachForMulticast(ctx, m)
	if err != nil {
		fmt.Printf("(util fcm) send message error: %v\n", err)
		return err
	}

	fmt.Printf("success count: %v\n", br.SuccessCount)
	for x, res := range br.Responses {
		if res.Success {
			fmt.Println("success: ", x)
		}
	}

	if br.FailureCount > 0 {
		var failedTokens []string
		errs := make(map[string]any)
		for idx, resp := range br.Responses {
			if !resp.Success {
				failedTokens = append(failedTokens, tokens[idx])
				errs[resp.Error.Error()] = resp.MessageID
			}
		}
		fmt.Println(len(failedTokens), " item List of tokens that caused failures: ", failedTokens)
		fmt.Printf("errors: %v\n", errs)
	}
	// return "ok"
	return nil
}
