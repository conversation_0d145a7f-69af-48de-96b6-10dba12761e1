package cast

import (
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"reflect"
	"strconv"
	"strings"
	"time"
)

func MapArrayToStruct(maps []map[string]any, variable any) error {
	structField := reflect.TypeOf(variable).Elem()
	structArray := reflect.ValueOf(variable).Elem()

	for _, m := range maps {
		newStruct := reflect.New(structField.Elem()).Elem()
		for k, v := range m {
			setField(newStruct, k, v)
		}
		structArray.Set(reflect.Append(structArray, newStruct))
	}

	return nil
}

func MapToStruct(m map[string]any, s any) error {
	for k, v := range m {
		setField(s, k, v)
	}
	return nil
}

func setField(m any, key string, value any) {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("key %s, value %v, err : %v\n", key, value, r)
		}
	}()
	if value == nil {
		return
	}

	var structValue reflect.Value
	switch res := m.(type) {
	case reflect.Value:
		structValue = res
	default:
		structValue = reflect.ValueOf(m).Elem()
	}

	structFieldValue := structValue.FieldByName(key)

	//if key not match, search for json tag
	if !structFieldValue.IsValid() {
		for i := 0; i < structValue.NumField(); i++ {
			field := structValue.Type().Field(i)
			if v, ok := field.Tag.Lookup("json"); ok {
				if v == key {
					structFieldValue = structValue.FieldByName(field.Name)
					break
				}
			}
		}
	}

	if !structFieldValue.IsValid() {
		//fmt.Printf("no such field: %s in obj\n", key)
		return
	}

	if !structFieldValue.CanSet() {
		fmt.Printf("can not set %s field value\n", key)
		return
	}

	structFieldType := structFieldValue.Type()
	val := reflect.ValueOf(value)

	//if data type from struct and map different, convert it
	if structFieldType != val.Type() {
		switch structFieldType.Kind() {
		case reflect.String:
			val = reflect.ValueOf(ToString(value))
		case reflect.Int:
			val = reflect.ValueOf(ToInt(value))
		case reflect.Int64:
			val = reflect.ValueOf(ToInt64(value))
		case reflect.Float64:
			val = reflect.ValueOf(ToFloat64(value))
		default:
			fmt.Printf("field %s type didn't match obj field type, type is %v while value is %v\n", key, structFieldType, val.Type())
			return
		}
	}

	structFieldValue.Set(val)
}

func ToString(data any) string {
	switch v := data.(type) {
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float32:
		return fmt.Sprintf("%f", v)
	case float64:
		return strconv.FormatFloat(v, 'f', 2, 64)
	case string:
		return v
	case []uint8:
		return string(v)
	case []any:
		return fmt.Sprintf("%s", v)
	case nil:
		return ""
	default:
		//check if data is map
		kind := reflect.TypeOf(data).Kind()
		if kind == reflect.Ptr {
			kind = reflect.TypeOf(data).Elem().Kind()
		}
		if kind == reflect.Map || kind == reflect.Struct || kind == reflect.Slice {
			jsonStr, err := json.Marshal(v)
			if err != nil {
				fmt.Println("failed converting to json:", err)
				return ""
			}
			return string(jsonStr)
		}
		fmt.Println("[ToString] - Invalid recognize data type toString. Type : ", reflect.TypeOf(data), " | Data : ", data)
		// stackSlice := make([]byte, 512)
		// s := runtime.Stack(stackSlice, false)
		// stacks := string(stackSlice[0:s])
		// fmt.Println(stacks)
		return fmt.Sprintf("%s", data)
	}
}

func ToInt(data any) int {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.Atoi(dataStr)
	if err != nil {
		switch i := data.(type) {
		case float32:
			return int(i)
		case float64:
			return int(i)
		default:
			fmt.Printf("failed converting '%v' to int, type is %v\n", data, reflect.TypeOf(data))
			return 0
		}
	} else {
		return result
	}
}

func ToInt64(data any) int64 {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.ParseInt(dataStr, 10, 64)
	if err != nil {
		return 0
	} else {
		return result
	}
}

func ToFloat64(data any) float64 {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.ParseFloat(dataStr, 64)
	if err != nil {
		fmt.Printf("failed converting '%v' to float64, type is %v\n", data, reflect.TypeOf(data))
		return 0
	} else {
		return result
	}
}

// StructToMap func
func StructToMap(item any) map[string]any {
	res := map[string]any{}
	if item == nil {
		return res
	}

	v := reflect.TypeOf(item)
	reflectValue := reflect.ValueOf(item)
	reflectValue = reflect.Indirect(reflectValue)

	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	for i := 0; i < v.NumField(); i++ {
		tag := v.Field(i).Tag.Get("json")
		field := reflectValue.Field(i).Interface()
		if tag != "" && tag != "-" {
			if v.Field(i).Type.Kind() == reflect.Struct {
				res[tag] = StructToMap(field)
			} else {
				res[tag] = field
			}
		}
	}
	return res
}

func dateIteration(start, end time.Time) func() time.Time {
	y, m, d := start.Date()
	start = time.Date(y, m, d, 0, 0, 0, 0, time.UTC)
	end = time.Date(y, m, d, 0, 0, 0, 0, time.UTC)
	return func() time.Time {
		if start.After(end) {
			return time.Time{}
		}
		date := start
		start = start.AddDate(0, 0, 1)
		return date
	}
}

func arrReverse(arr []string) []string {
	if len(arr) == 0 {
		return arr
	}
	return append(arrReverse(arr[1:]), arr[0])
}

// DateReverse reversing date format
func DateReverse(date string) string {
	arrDate := strings.Split(date, "-")
	dateRev := arrReverse(arrDate)
	return strings.Join(dateRev, "-")
}

// DateRanges get date reanges func
func dateIteraion(start, end time.Time) func() time.Time {
	y, m, d := start.Date()
	start = time.Date(y, m, d, 0, 0, 0, 0, time.UTC)
	y, m, d = end.Date()
	end = time.Date(y, m, d, 0, 0, 0, 0, time.UTC)

	return func() time.Time {
		if start.After(end) {
			return time.Time{}
		}
		date := start
		start = start.AddDate(0, 0, 1)
		return date
	}
}

// DateRanges func *get date range*
func DateRanges(s, e string) []string {
	start, _ := time.Parse("2006-01-02", s)
	end, _ := time.Parse("2006-01-02", e)
	var dates []string
	for rd := dateIteraion(start, end); ; {
		date := rd()
		if date.IsZero() {
			break
		}
		dates = append(dates, date.Format("2006-01-02"))
	}
	return dates
}

// GetDateRanges func *get date range*
func GetDateRanges(s, e string) map[string]any {
	start, _ := time.Parse("2006-01-02", s)
	end, _ := time.Parse("2006-01-02", e)
	dates := make(map[string]any, 0)
	for rd := dateIteraion(start, end); ; {
		date := rd()
		if date.IsZero() {
			break
		}
		dates[strconv.Itoa(date.Day())] = date.Format("2006-01-02")
	}
	return dates
}

// DaysIn count the number of days in 1 month
func DaysIn(m time.Month, year int) int {
	return time.Date(year, m+1, 0, 0, 0, 0, 0, time.UTC).Day()
}

// HashS hasing using sha256
func HashS(s string) string {
	sha := sha256.New()
	sha.Write([]byte(s))
	bs := sha.Sum(nil)
	j := fmt.Sprintf("%x\n", bs)
	return j
}

// ArrayLenValue count array len value
func ArrayLenValue(arr []int) map[int]int {
	// create a dict of values for each element
	dict := make(map[int]int)
	for _, num := range arr {
		dict[num] = dict[num] + 1
	}
	return dict
}

// ArrayGroup func grouping slice
func ArrayGroup(arr []map[string]any, key string) []map[any][]map[string]any {
	var filter []map[any][]map[string]any
	fil := make(map[any][]map[string]any)
	for _, v := range arr {
		strType := reflect.TypeOf("")
		intType := reflect.TypeOf(1)
		if reflect.TypeOf(v[key]) == strType {
			fil[v[key].(string)] = append(fil[v[key].(string)], v)
		} else if reflect.TypeOf(v[key]) == intType {
			fil[v[key].(int)] = append(fil[v[key].(int)], v)
		}
	}
	filter = append(filter, fil)
	return filter
}

// StringInSlice check string in slice
func StringInSlice(a string, list []string) bool {
	for _, b := range list {
		if b == a {
			return true
		}
	}
	return false
}

// Distance get distance in km func
func Distance(lat1 float64, lng1 float64, lat2 float64, lng2 float64, unit ...string) float64 {
	const PI float64 = 3.141592653589793

	radlat1 := float64(PI * lat1 / 180)
	radlat2 := float64(PI * lat2 / 180)

	theta := float64(lng1 - lng2)
	radtheta := float64(PI * theta / 180)

	dist := math.Sin(radlat1)*math.Sin(radlat2) + math.Cos(radlat1)*math.Cos(radlat2)*math.Cos(radtheta)

	if dist > 1 {
		dist = 1
	}

	dist = math.Acos(dist)
	dist = dist * 180 / PI
	dist = dist * 60 * 1.1515

	if len(unit) > 0 {
		if unit[0] == "K" {
			dist = dist * 1.609344
		} else if unit[0] == "N" {
			dist = dist * 0.8684
		}
	}

	return dist
}

func JsonToStruct(data any, structVariable any) any {
	var err error
	switch v := data.(type) {
	case []byte:
		err = json.Unmarshal(v, &structVariable)
		if err != nil {
			fmt.Printf("failed converting '%v' to struct, type is %v\n", data, reflect.TypeOf(data))
			return nil
		}
		return structVariable
	case string:
		err = json.Unmarshal([]byte(v), &structVariable)
		if err != nil {
			fmt.Printf("failed converting '%v' to struct, type is %v\n", data, reflect.TypeOf(data))
			return nil
		}
		return structVariable
	default:
		fmt.Printf("[JsonToStruct] failed converting '%v' to struct, type is %v\n", data, reflect.TypeOf(data))
		return nil
	}
}

func JsonToMap(data any) (map[string]any, error) {
	var objmap map[string]any
	var err error
	switch v := data.(type) {
	case error:
		s := v.Error()
		err = json.Unmarshal([]byte(s), &objmap)
		return objmap, err
	case string:
		err = json.Unmarshal([]byte(v), &objmap)
		return objmap, err
	}

	return objmap, err
}

func RandStringBytes(n int) string {
	const letterBytes = "abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	b := make([]byte, n)
	for i := range b {
		b[i] = letterBytes[rand.Intn(len(letterBytes))]
	}
	return string(b)
}

func MakeTimestamp() int64 {
	loc, _ := time.LoadLocation("Asia/Jakarta")
	return time.Now().In(loc).UnixNano() / int64(time.Millisecond)
}

func MakeTimestampAdd() int64 {
	loc, _ := time.LoadLocation("Asia/Jakarta")
	return time.Now().In(loc).AddDate(0, 0, 3).UnixNano() / int64(time.Millisecond)
}

func ContainsStr(lstr []string, c string) bool {
	for _, v := range lstr {
		if v == c {
			return true
		}
	}
	return false
}

// ToIntSlice converts a slice of any to a slice of int
func ToIntSlice(data []any) []int {
	result := make([]int, len(data))
	for i, v := range data {
		result[i] = ToInt(v)
	}
	return result
}
