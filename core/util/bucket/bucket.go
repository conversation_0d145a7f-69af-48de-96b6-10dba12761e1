package bucket

import (
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/url"
	"os"
	"time"

	"cloud.google.com/go/storage"
	log "gitlab.com/backend/api-hrm/core/log"
	"google.golang.org/api/option"
)

var bucketClient *storage.Client

func getBucketClient() *storage.Client {
	if bucketClient == nil {
		ctx := context.Background()
		bucketCredentialPath := os.Getenv("BUCKET_CREDENTIAL_PATH")
		if bucketCredentialPath == "" {
			bucketCredentialPath = "assets/rsa/uniq-187911-4d685111920f.json"
		}
		var err error
		bucketClient, err = storage.NewClient(ctx, option.WithCredentialsFile(bucketCredentialPath))
		if err != nil {
			log.IfError(err)
		}
	}
	return bucketClient
}

// UploadBucket upload google storage
func UploadBucket(file multipart.File, filename string, empID string, public bool) (string, error) {
	bucket := "uniq-187911.appspot.com"
	env := os.Getenv("ENV")
	gs := "https://storage.googleapis.com"
	object := "hrm/" + env + "/api/" + empID + "/" + filename
	entity := storage.AllUsers
	role := storage.RoleOwner

	ctx := context.Background()
	client := getBucketClient()
	if client == nil {
		return "Error", fmt.Errorf("client is nil")
	}
	defer client.Close()

	bh := client.Bucket(bucket)
	obj := bh.Object(object)
	wc := obj.NewWriter(ctx)
	if _, err := io.Copy(wc, file); err != nil {
		return "Error", err
	}
	if err := wc.Close(); err != nil {
		return "Error", err
	}
	acl := client.Bucket(bucket).DefaultObjectACL()

	if public {
		if err := acl.Set(ctx, entity, role); err != nil {
			return "Error", err
		}
	}
	attrs, err := obj.Attrs(ctx)
	log.IfError(err)

	u, err := url.Parse("/" + bucket + "/" + attrs.Name)
	if err != nil {
		return "Error", err
	}
	url := gs + u.EscapedPath()
	fmt.Printf("File %v uploaded, url: %v\n", filename, url)
	return url, nil
}

func DeleteBucket(bucket, object string, public bool) error {
	entity := storage.AllUsers
	role := storage.RoleOwner
	ctx := context.Background()
	client := getBucketClient()
	if client == nil {
		return fmt.Errorf("client is nil")
	}

	defer client.Close()
	acl := client.Bucket(bucket).DefaultObjectACL()

	if public {
		if err := acl.Set(ctx, entity, role); err != nil {
			return err
		}
	}

	ctx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()
	o := client.Bucket(bucket).Object(object)
	if err := o.Delete(ctx); err != nil {
		return fmt.Errorf("Object(%q).Delete: %v", object, err)
	}
	fmt.Printf("Blob %v deleted.\n", object)
	return nil
}
