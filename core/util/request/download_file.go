package request

import (
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
)

// SaveToTempDir saves the uploaded file (from fileHeader) into a newly-created
// subdirectory of the system temp directory. It returns the full path to the saved file.
// The caller is responsible for cleaning up the returned directory when done.
func SaveToTempDir(fileHeader *multipart.FileHeader) (savedPath string, err error) {
	// 1. Create a unique temp subdir under the OS temp dir
	sysTemp := os.TempDir()
	tmpDir, err := os.MkdirTemp(sysTemp, "upload-*")
	if err != nil {
		return "", fmt.Errorf("cannot create temp dir: %w", err)
	}

	// 2. Open the uploaded file
	src, err := fileHeader.Open()
	if err != nil {
		// cleanup on error
		os.RemoveAll(tmpDir)
		return "", fmt.Errorf("cannot open uploaded file: %w", err)
	}
	defer src.Close()

	// 3. Create the destination file inside our temp dir
	savedPath = filepath.Join(tmpDir, fileHeader.Filename)
	dst, err := os.Create(savedPath)
	if err != nil {
		os.RemoveAll(tmpDir)
		return "", fmt.Errorf("cannot create dest file: %w", err)
	}
	defer dst.Close()

	// 4. Copy the contents over
	if _, err := io.Copy(dst, src); err != nil {
		os.RemoveAll(tmpDir)
		return "", fmt.Errorf("failed to save file: %w", err)
	}

	// 5. Return the full path (caller can inspect tmpDir via filepath.Dir(savedPath))
	return savedPath, nil
}
