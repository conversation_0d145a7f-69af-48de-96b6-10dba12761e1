package log

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"net/http"
	"text/template"
)

type SlackHook struct {
	HookUrl string
	Channel string
}

func (slack *SlackHook) send(out logOutput) error {
	if slack.HookUrl == "" {
		return nil
	}
	slackTmpl, err := template.ParseFiles("assets/templates/slack_template.tmpl")
	if err != nil {
		return err
	}

	var reqBody bytes.Buffer
	err = slackTmpl.Execute(&reqBody, out)
	if err != nil {
		return err
	}

	resp, err := http.Post(slack.HookUrl, "application/json", bytes.NewBuffer(reqBody.Bytes()))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("read body err", err)
	}
	fmt.Println("slack:", resp.StatusCode, string(body))
	return nil
}
