package log

import (
	"fmt"
	"runtime"
	"strings"
)

func GetCaller(searchFile string) string {
	// Skip 1 to skip the printCallers function itself
	var pc uintptr
	var file string
	var line int
	var ok bool
	for i := 1; ; i++ {
		pc, file, line, ok = runtime.Caller(i)
		if !ok {
			break
		}
		fn := runtime.FuncForPC(pc)
		fmt.Printf("Frame %d: %s:%d %s\n", i, file, line, fn.Name())
		if strings.Contains(file, searchFile) {
			return fmt.Sprintf("%v:%v", file, line)
		}
	}
	return fmt.Sprintf("%v:%v", file, line)
}
