package log

import (
	"fmt"
	"regexp"
	"runtime"
	"strings"
	"time"
)

type logger struct {
	Config LoggerConfig
	hook   hook
}

type LoggerConfig struct {
	DisableColor bool
	HideTime     bool
	TimeOffset   int64
	TimeFormat   string
}

type logOutput struct {
	Message    string
	Stacks     string
	Level      string
	LevelColor string
}

type hook interface {
	send(logOutput) error
}

var (
	sdt         = New()
	prefixError = "\u001B[1;31m[ERROR]\u001B[0m"
	prefixInfo  = "\033[1;32m[INFO]\033[0m"
)

func New() *logger {
	return &logger{
		Config: LoggerConfig{
			DisableColor: true,
			HideTime:     true,
			TimeOffset:   25200,
			TimeFormat:   "02/01/2006 15:04:05",
		},
	}
}

func SetLogger(config LoggerConfig) {
	sdt.setLogConfig(config)
}

func AddHook(h hook) {
	sdt.hook = h
}

func (l *logger) setLogConfig(config LoggerConfig) {
	l.Config = config
}

func IfError(err error) bool {
	if err != nil {
		stackSlice := make([]byte, 512)
		s := runtime.Stack(stackSlice, false)
		stacks := string(stackSlice[0:s])
		stacks = beautifyStacks(stacks)

		output := logOutput{
			Message:    err.Error(),
			Stacks:     stacks,
			Level:      "[ERROR]",
			LevelColor: prefixError,
		}

		if sdt.hook != nil {
			errHook := sdt.hook.send(output)
			if errHook != nil {
			}
		}
		fmt.Println(sdt.outputFormat(output))
		return true
	}
	return false
}

func Info(msg string, args ...interface{}) {
	stackSlice := make([]byte, 512)
	s := runtime.Stack(stackSlice, false)
	stacks := string(stackSlice[0:s])
	stacks = beautifyStacks(stacks)

	output := logOutput{
		Message:    fmt.Sprintf(msg, args...),
		Stacks:     stacks,
		Level:      "[INFO]",
		LevelColor: prefixInfo,
	}
	fmt.Println(sdt.outputFormat(output))
}

func (l logger) outputFormat(out logOutput) string {
	args := make([]string, 0)

	if l.Config.DisableColor {
		args = append(args, out.Level)
	} else {
		args = append(args, out.LevelColor)
	}
	if !l.Config.HideTime {
		tm := time.Unix(time.Now().Unix()+l.Config.TimeOffset, 0).Format(l.Config.TimeFormat)
		args = append(args, tm)
	}

	args = append(args, out.Message)
	args = append(args, "\n")
	args = append(args, out.Stacks)
	return fmt.Sprintf(strings.Join(args, " "))
}

func beautifyStacks(stacks string) (result string) {
	defer func() {
		if r := recover(); r != nil {
			result = stacks
		}
	}()

	rex, _ := regexp.Compile(`[/a-zA-Z\d\-_/]+\.go:\d+\s`)
	callers := rex.FindAllString(stacks, -1)

	rex, _ = regexp.Compile(`([a-zA-Z]+\.{1,3})+[a-zA-Z]+((\([a-zA-Z0-9\s,\.]*\))|)\012`)
	functions := rex.FindAllString(stacks, -1)
	funcNames := make([]string, 0)
	for _, x := range functions {
		fName := strings.Replace(x, "\n", "", 1)
		param := regexp.MustCompile(`\(.+\)`).FindString(fName)
		if param != "" {
			fName = strings.Replace(fName, param, "()", 1)
		}
		funcNames = append(funcNames, fName)
	}

	for i, f := range callers {
		if i == 0 {
			continue
		}
		fName := ""
		if len(funcNames) > i {
			fName = funcNames[i]
		}
		result += fmt.Sprintf("\tat %s   %s\n", fName, strings.TrimSpace(f))
	}
	return result
}
