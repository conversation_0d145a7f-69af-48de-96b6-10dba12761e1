package mysql

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"

	"gitlab.com/backend/api-hrm/core/util/cast"
)

type SqlResult struct {
	Data     interface{}
	Error    error
	SqlQuery string
}

func (s *SqlResult) Model(model interface{}) error {
	if s.Error != nil {
		return s.Error
	}

	//return mapstructure.Decode(s.Result[0], model)
	//e, _ := json.Marshal(s.Data)
	//fmt.Println(string(e))

	if reflect.TypeOf(model).Kind() != reflect.Ptr {
		return errors.New("model should be pointer")
	}

	if m, ok := s.Data.(map[string]interface{}); ok {
		return cast.MapToStruct(m, model)
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			if reflect.ValueOf(model).Elem().Kind() == reflect.Slice {
				//jsonStr, err := json.Marshal(arr)
				//if err != nil {
				//	return err
				//}
				//return json.Unmarshal(jsonStr, model)
				return cast.MapArrayToStruct(arr, model)
			} else {
				return cast.MapToStruct(arr[0], model)
			}
		} else {
			return nil
		}
	}
	return errors.New("model is not single map")
}

func (s *SqlResult) Map() (map[string]interface{}, error) {
	if s.Error != nil {
		return nil, s.Error
	}

	if m, ok := s.Data.(map[string]interface{}); ok {
		return m, nil
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			return arr[0], nil
		} else {
			return map[string]interface{}{}, nil
		}
	}
	return nil, fmt.Errorf("model is not single map, %v", reflect.TypeOf(s.Data))
}

func (s *SqlResult) ArrayMap() ([]map[string]interface{}, error) {
	if s.Error != nil {
		return nil, s.Error
	}

	if m, ok := s.Data.(map[string]interface{}); ok {
		return []map[string]interface{}{m}, nil
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			return arr, nil
		} else {
			return []map[string]interface{}{}, nil
		}
	}
	return nil, fmt.Errorf("model is not single map, %v", reflect.TypeOf(s.Data))
}

func (s *SqlResult) MustJson() string {
	jsonStr, err := json.Marshal(s.Data)
	if err != nil {
		fmt.Println("failed converting to json:", err)
		return ""
	}
	return string(jsonStr)
}

func (s *SqlResult) PrintSql() *SqlResult {
	fmt.Println(s.SqlQuery)
	return s
}
