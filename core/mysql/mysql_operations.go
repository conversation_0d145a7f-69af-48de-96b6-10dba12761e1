package mysql

import (
	"database/sql"
	"fmt"
	"log"
	"strings"

	"gitlab.com/backend/api-hrm/core/util/cast"
)

// Repository struct
type Repository struct {
	Conn *sql.DB
}

// Query sql
func (db *Repository) Query(sql string, args ...any) (result *SqlResult) {
	tableData := make([]map[string]any, 0)
	result = &SqlResult{Data: tableData}

	rows, err := db.Conn.Query(sql, args...)
	if err != nil {
		result.Error = err
		fmt.Println(err)
		fmt.Println("sql err: ", getSQLRaw(sql, args...))
		fmt.Printf("sql: %v \n args: %v \n", sql, args)
		return
	}

	result.SqlQuery = getSQLRaw(sql, args...)

	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		result.Error = err
		return
	}

	count := len(columns)
	values := make([]any, count)
	scanArgs := make([]any, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			result.Error = err
			return
		}

		entry := make(map[string]any)
		for i, col := range columns {
			v := values[i]
			if b, ok := v.([]byte); ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	result.Error = rows.Err()
	result.Data = tableData
	return
}

// QueryArrayOld get query
func (db *Repository) QueryArrayOld(sql string, args ...any) ([]map[string]any, error) {
	tableData := make([]map[string]any, 0)
	rows, err := db.Conn.Query(sql, args...)
	if err != nil {
		fmt.Println(err)
		fmt.Println("sql err: ", getSQLRaw(sql, args...))
		return tableData, err
	}

	defer func() {
		err := rows.Close()
		if err != nil {
			log.Println("closing sql row error")
		}
	}()

	columns, err := rows.Columns()
	if err != nil {
		return tableData, err
	}

	count := len(columns)
	values := make([]any, count)
	scanArgs := make([]any, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return tableData, err
		}

		entry := make(map[string]any)
		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	err = rows.Err()
	if err != nil {
		return tableData, err
	}

	return tableData, nil
}

// Insert func
func (db *Repository) Insert(table string, data map[string]any) (sql.Result, error) {
	query, values := BuildInsertQuery(table, data)
	res, err := db.Conn.Exec(query, values...)
	if err != nil {
		fmt.Println(err)
		fmt.Println("sql err: ", getSQLRaw(query, values...))
	}
	return res, err
}

// InsertGetLastID func
func (db *Repository) InsertGetLastID(table string, data map[string]any) (sql.Result, int64, error) {
	res, err := db.Insert(table, data)
	if err != nil {
		return nil, 0, err
	}
	lid, err := res.LastInsertId()
	return res, lid, err
}

// BulkInsert insert bulk
func (db *Repository) BulkInsert(table string, data []map[string]any) (sql.Result, error) {
	query, values := BuildBulkInsertQuery(table, data)
	res, err := db.Conn.Exec(query, values...)
	if err != nil {
		fmt.Println(err)
		fmt.Println("sql err: ", getSQLRaw(query, values...))
	}
	return res, err
}

// Updates func
func (db *Repository) Updates(table string, data map[string]any, where map[string]any) (sql.Result, error) {
	query, params := BuildUpdateQuery(table, data, where)
	res, err := db.Conn.Exec(query, params...)
	return res, err
}

// SingleUpdate single update query
func (db *Repository) SingleUpdate(table string, where string, data []map[string]any) (sql.Result, error) {
	var key []string
	query := "UPDATE " + table
	fmt.Println(data)
	for _, a := range data {
		query += " SET "
		for b := range a {
			query += b + " = ?, "
			key = append(key, b)
		}
		query = query[:len(query)-2]
		break
	}

	query += " WHERE " + where + " = ?"

	val := []any{}
	for _, v := range data {
		for _, w := range key {
			val = append(val, v[w])
		}
		val = append(val, v[where])
	}

	res, err := db.Conn.Exec(query, val...)
	return res, err
}

// BulkUpdate bulk update query
func (db *Repository) BulkUpdate(table string, where string, data []map[string]any) error {
	var key []string
	query := "UPDATE " + table
	for _, a := range data {
		query += " SET "
		for b := range a {
			if b != where {
				query += b + " = ?, "
				key = append(key, b)
			}
		}
		query = query[:len(query)-2]
		break
	}

	query += " WHERE " + where + " = ?"

	arrval := [][]any{}
	val := make([]any, 0)
	for _, v := range data {
		for _, w := range key {
			val = append(val, v[w])
		}
		val = append(val, v[where])
		arrval = append(arrval, val)
		val = []any{}
	}

	tx, err := db.Conn.Begin()
	if err != nil {
		fmt.Println(err)
		fmt.Println("sql err: ", getSQLRaw(query, val...))
		return err
	}

	stt, err := tx.Prepare(query)
	if err != nil {
		fmt.Println(err)
		fmt.Println("sql err: ", getSQLRaw(query, val...))
		return err
	}

	for _, k := range arrval {
		_, err := stt.Exec(k...)
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return nil
}

// Deletes func
func (db *Repository) Deletes(table string, where map[string]any) (sql.Result, error) {
	query, wheres := BuildDeleteQuery(table, where)
	res, err := db.Conn.Exec(query, wheres...)
	if err != nil {
		fmt.Println(err)
		fmt.Println("sql err: ", getSQLRaw(query, wheres...))
	}
	return res, err
}

// Deletes func
func (db *Repository) BulkDeletes(table string, id []string, where string) (sql.Result, error) {
	wheres := make([]any, 0)
	query := "DELETE FROM " + table + " WHERE " + where + " IN ("
	if len(id) > 0 {
		for _, v := range id {
			query += "?, "
			wheres = append(wheres, v)
		}

	}
	query = query[:len(query)-2] + ")"
	res, err := db.Conn.Exec(query, wheres...)
	return res, err
}

func getSQLRaw(sql string, params ...any) string {
	for i := 0; i < len(params); i++ {
		index := strings.Index(sql, "?")
		sql = sql[:index] + cast.ToString(params[i]) + sql[index+1:]
	}
	return sql
}

// QueryWhereAnd where and
func (db *Repository) QueryWhereAnd(sql string, args []any) ([]map[string]any, error) {
	tableData := make([]map[string]any, 0)
	rows, err := db.Conn.Query(sql, args...)
	if err != nil {
		return tableData, err
	}

	defer func() {
		err := rows.Close()
		if err != nil {
			log.Println("closing sql row error")
		}
	}()

	columns, err := rows.Columns()
	if err != nil {
		return tableData, err
	}

	count := len(columns)
	values := make([]any, count)
	scanArgs := make([]any, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return tableData, err
		}

		entry := make(map[string]any)
		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	err = rows.Err()
	if err != nil {
		return tableData, err
	}

	return tableData, nil
}
