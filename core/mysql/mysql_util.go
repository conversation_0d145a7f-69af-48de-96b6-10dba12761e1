package mysql

import (
	"fmt"
	"reflect"
	"regexp"
	"sort"
	"strings"

	"gitlab.com/backend/api-hrm/core/util/cast"
)

// WhereIn generates a SQL IN clause with the appropriate number of placeholders.
// It takes an integer size parameter and returns a string containing that many
// comma-separated question marks enclosed in parentheses.
//
// For example:
// WhereIn(3) returns "(?,?,?)"
// WhereIn(1) returns "(?)"
// WhereIn(0) returns "()"
//
// This is typically used for SQL queries with variable-length IN clauses.
func WhereIn(size int) string {
	whereIn := strings.Repeat("?,", size)
	whereIn = strings.TrimRight(whereIn, ",")
	return fmt.Sprintf("(%s)", whereIn)
}

// MapParam maps named parameters in an SQL query string with their corresponding values.
// It supports both direct string substitution (using a "$" prefix) and placeholder replacement
// (using an "@" prefix).
//
// The function works in two main steps:
//  1. It iterates over the provided params map to replace occurrences of "$<key>" in the SQL string
//     with the string representation of the corresponding value. This handles literal replacements.
//  2. It then uses a regular expression to find placeholders in the SQL string denoted by "@<key>".
//     For each found placeholder:
//     - If the corresponding parameter value is a slice, it flattens the slice (handling nested slices)
//     and replaces the placeholder with an "IN" clause generated by the WhereIn function, inserting the
//     appropriate number of "?" placeholders.
//     - If the parameter value is not a slice, it replaces the placeholder with a single "?" and adds the
//     value to the result slice of interface{}.
//
// The function returns the modified SQL string along with a slice of parameter values in the order
// they should be passed to the database query.
func MapParam(sql string, params map[string]any) (string, []interface{}) {
	for key, v := range params {
		sql = strings.Replace(sql, fmt.Sprintf("$%s", key), cast.ToString(v), -1)
	}

	r, _ := regexp.Compile("@[a-zA-Z]+")

	result := make([]interface{}, 0)
	for {
		key := r.FindString(sql)
		if key != "" { //_, ok := params[key]; key != "" && ok
			key = strings.Replace(key, "@", "", 1)
			sql = strings.Replace(sql, fmt.Sprintf("(@%s)", key), fmt.Sprintf("@%s", key), 1)
			if s := reflect.ValueOf(params[key]); s.Kind() == reflect.Slice {
				arrLenght := 0
				for i := 0; i < s.Len(); i++ {
					if values := reflect.ValueOf(s.Index(i).Interface()); values.Kind() == reflect.Slice {
						for j := 0; j < values.Len(); j++ {
							result = append(result, values.Index(j).Interface())
							arrLenght += 1
						}
					} else {
						result = append(result, s.Index(i).Interface())
						arrLenght += 1
					}
				}
				sql = strings.Replace(sql, fmt.Sprintf("@%s", key), WhereIn(arrLenght), 1)
			} else {
				if params[key] != nil {
					result = append(result, params[key])
				} else {
					fmt.Printf("Map Data with key '%s' Not Found! \n", key)
				}
				sql = strings.Replace(sql, fmt.Sprintf("@%s", key), " ? ", 1)
			}
		} else {
			break
		}
	}

	return sql, result
}

// func BuildInsertQuery(table string, data []map[string]any) (string, []any) {
// 	values := make([]any, 0)
// 	query := "INSERT INTO " + table
// 	var key []string

// 	for _, t := range data {
// 		query += "("
// 		for u := range t {
// 			query += u + ", "
// 			key = append(key, u)
// 		}
// 		query = query[:len(query)-2]
// 		query += ") VALUES "
// 	}

// 	for _, v := range data {
// 		query += "("
// 		for _, w := range key {
// 			values = append(values, v[w])
// 			query += "?, "
// 		}
// 		query = query[:len(query)-2]
// 		query += "), "
// 	}
// 	query = query[:len(query)-2]
// 	return query, values
// }

func BuildInsertQuery(table string, data map[string]any) (string, []any) {
	values := make([]any, 0)
	query := "INSERT INTO " + table + " ("
	for col, val := range data {
		query += col + ","
		values = append(values, val)
	}
	query = query[:len(query)-1] + ") VALUES (" + strings.Repeat("?, ", len(data)-1) + "?) "
	return query, values
}

// BuildBulkInsertQuery builds a multi‐row INSERT for MySQL.
// - table is the table name.
// - data is a slice of maps, each map being column→value for one row.
// It returns the query string (with "?" placeholders) and a flat args slice.
func BuildBulkInsertQuery(table string, data []map[string]any) (string, []any) {
	if len(data) == 0 {
		return "", nil
	}

	// 1) Gather all column names
	colSet := make(map[string]struct{}, len(data)*2)
	for _, row := range data {
		for col := range row {
			colSet[col] = struct{}{}
		}
	}

	// 2) Sort columns for a stable order
	cols := make([]string, 0, len(colSet))
	for col := range colSet {
		cols = append(cols, col)
	}
	sort.Strings(cols)

	// 3) Build one "(?,?,…)" placeholder per row, and collect args
	placeholders := make([]string, 0, len(data))
	args := make([]any, 0, len(data)*len(cols))

	for _, row := range data {
		// build a placeholder group for this row
		ph := make([]string, 0, len(cols))
		for _, col := range cols {
			ph = append(ph, "?")
			if val, ok := row[col]; ok {
				args = append(args, val)
			} else {
				// missing key → use NULL
				args = append(args, nil)
			}
		}
		placeholders = append(placeholders, "("+strings.Join(ph, ",")+")")
	}

	// 4) Assemble final query
	columnList := strings.Join(cols, ",")
	placeholderList := strings.Join(placeholders, ",")
	query := fmt.Sprintf(
		"INSERT INTO %s (%s) VALUES %s",
		table, columnList, placeholderList,
	)

	return query, args
}

func BuildUpdateQuery(table string, data map[string]any, where map[string]any) (string, []any) {
	wheres := make([]any, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		query += col + "=" + fmt.Sprintf("'%v'", val) + ", "
	}
	query = query[:len(query)-2]
	query += " WHERE "
	if len(where) > 1 {
		for col, val := range where {
			query += col + "=? AND "
			wheres = append(wheres, val)
		}
		query = query[:len(query)-4]
	} else {
		for col, val := range where {
			query += col + "=?"
			wheres = append(wheres, val)
		}
	}
	return query, wheres
}

// BuildDeleteQuery builds a DELETE query with WHERE conditions
func BuildDeleteQuery(table string, where map[string]any) (string, []any) {
	wheres := make([]any, 0)
	query := "DELETE FROM " + table

	if len(where) > 1 {
		query += " WHERE "
		for col, val := range where {
			query += fmt.Sprintf("%v", col) + "=? AND "
			wheres = append(wheres, val)
		}
		query = query[:len(query)-5]
	} else {
		for col, val := range where {
			query += " WHERE " + fmt.Sprintf("%v", col) + "=?"
			wheres = append(wheres, val)
		}
	}

	return query, wheres
}
