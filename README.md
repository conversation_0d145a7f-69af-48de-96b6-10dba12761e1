### API HRM
this app serve as backend to handle any request related to HR App

## REQUISITES
 - create jwt private and public key
```aidl
openssl genrsa -out assets/rsa/priv.key 2048
openssl rsa -in assets/rsa/priv.key -pubout > assets/rsa/pub.key
```


## Swagger
First make you you have installed [swaggo](https://github.com/swaggo/swag) :   
```
go install github.com/swaggo/swag/cmd/swag@latest
```

To generate swagger documentation:
```
$HOME/go/bin/swag init --parseDependency
```