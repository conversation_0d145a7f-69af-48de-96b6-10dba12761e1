package usecase

import (
	"gitlab.com/backend/api-hrm/domain"
)

type messagingUseCase struct {
	messagingRepository domain.MessagingRepository
}

func NewMessagingUseCase(me domain.MessagingRepository) domain.MessagingUseCase {
	return &messagingUseCase{messagingRepository: me}
}

func (me *messagingUseCase) AddMessage(message map[string]interface{}) error {
	return me.messagingRepository.AddMessage(message)
}

func (me *messagingUseCase) AddDeviceInfo(info domain.DeviceCreateRequest, userType string, userID string) error {
	return me.messagingRepository.AddDeviceInfo(info, userType, userID)
}

func (me *messagingUseCase) FetchUserDevice(empID int) ([]domain.Device, error) {
	return me.messagingRepository.FetchUserDevice(empID)
}

func (me *messagingUseCase) FetchMessages(empID int, tipe string) ([]domain.Message, error) {
	return me.messagingRepository.FetchMessages(empID, tipe)
}

func (me *messagingUseCase) DeleteNotif(notifID int) error {
	return me.messagingRepository.DeleteNotif(notifID)
}

func (me *messagingUseCase) UpdateMessageView(data []map[string]interface{}) error {
	return me.messagingRepository.UpdateMessageView(data)
}

func (me *messagingUseCase) UpdateMessageRead(data []map[string]interface{}) error {
	return me.messagingRepository.UpdateMessageRead(data)
}

func (me *messagingUseCase) DeleteMessages(msgID []string) error {
	return me.messagingRepository.DeleteMessages(msgID)
}

func (me *messagingUseCase) FetchUserDeviceByToken(token string) (domain.Device, error) {
	return me.messagingRepository.FetchUserDeviceByToken(token)
}

func (me *messagingUseCase) UpdateUserDevice(data map[string]interface{}, where string) error {
	return me.messagingRepository.UpdateUserDevice(data, where)
}
