package http

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"gitlab.com/backend/api-hrm/domain"
)

type MessagingHandler struct {
	MessagingUseCase domain.MessagingUseCase
}

func NewMessagingHandler(app *fiber.App, me domain.MessagingUseCase) {
	handler := &MessagingHandler{MessagingUseCase: me}
	v2 := app.Group("/v2")
	v2.Get("/user_devices/:emp_id", handler.FetchUserDevice)
	v2.Get("/fetch_messages/:type/:emp_id", handler.FetchMessages)
	v2.Get("/fetch_messages", handler.FetchMessages)
	v2.Get("/fetch_device/:token", handler.FetchUserDeviceByToken)
	v2.Post("/update_user_device", handler.UpdateUserDevice)
	v2.Post("/add_message", handler.AddMessage)
	v2.Post("/add_device", handler.AddDeviceInfo)
	v2.Post("/update_message_view", handler.UpdateMessageView)
	v2.Post("/delete_notif", handler.DeleteNotif)
	v2.Post("/update_message_read", handler.UpdateMessageRead)
	v2.Post("/delete_messages", handler.DeleteMessages)
}

// AddDeviceInfo handle func
// @Summary Register a device for notifications
// @Description Register a new device with its token for push notifications
// @Tags messaging
// @Accept json
// @Produce json
// @Param device body domain.DeviceCreateRequest true "Device information"
// @Success 200 {object} object "Success response with status"
// @Failure 400 {object} object "Bad request error"
// @Failure 401 {object} object "Unauthorized error"
// @Failure 500 {object} object "Internal server error"
// @Router /v2/add_device [post]
func (mh *MessagingHandler) AddDeviceInfo(c *fiber.Ctx) error {
	var deviceInfo domain.DeviceCreateRequest

	if err := c.BodyParser(&deviceInfo); err != nil {
		fmt.Printf("form parsing error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "form parsing error",
			"error":   err,
		})
	}

	user := domain.GetUserSessionFiber(c)
	err := mh.MessagingUseCase.AddDeviceInfo(deviceInfo, user.UserType, cast.ToString(user.UserId))
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"status":  0,
			"message": err,
		})
	}
	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"status":  1,
		"message": "Add device succesfully",
	})
}

// FetchUserDevice handle func
// @Summary Get user devices
// @Description Get all registered devices for a specific user
// @Tags messaging
// @Accept json
// @Produce json
// @Param emp_id path int true "Employee ID"
// @Success 200 {array} domain.Device "List of user devices"
// @Failure 500 {object} object "Internal server error"
// @Router /v2/user_devices/{emp_id} [get]
func (mh *MessagingHandler) FetchUserDevice(c *fiber.Ctx) error {
	emp_id := c.Params("emp_id")
	id, _ := strconv.Atoi(emp_id)
	res, err := mh.MessagingUseCase.FetchUserDevice(id)
	if err != nil {
		fmt.Printf("fetching user device error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"status":  0,
			"message": err,
		})
	}

	return c.JSON(res)
}

// FetchUserDeviceByToken handle func
// @Summary Get user device by token
// @Description Get device information by FCM token
// @Tags messaging
// @Accept json
// @Produce json
// @Param token path string true "FCM token"
// @Success 200 {object} domain.Device "Device information"
// @Failure 400 {object} object "Bad request error"
// @Router /v2/fetch_device/{token} [get]
func (mh *MessagingHandler) FetchUserDeviceByToken(c *fiber.Ctx) error {
	// form, err := c.MultipartForm()
	// if err != nil {
	// 	log.IfError(err)
	// 	return c.Status(fiber.ErrBadRequest.Code).JSON(&fiber.Map{
	// 		"message": "multipart form error",
	// 		"error":   err,
	// 		"status":  fiber.ErrBadRequest,
	// 	})
	// }
	// token := form.Value["token"][0]

	token := c.Params("token")
	fmt.Println(token)

	res, err := mh.MessagingUseCase.FetchUserDeviceByToken(token)
	if err != nil {
		log.IfError(err)
		return c.Status(fiber.ErrBadRequest.Code).JSON(&fiber.Map{
			"message": "fetching user device by token error",
			"error":   err,
			"status":  fiber.ErrBadRequest.Code,
		})
	}
	return c.JSON(res)
}

// AddMessage handle func
// @Summary Add a new notification message
// @Description Create a new notification message for a user
// @Tags messaging
// @Accept multipart/form-data
// @Produce json
// @Param title formData string true "Message title"
// @Param message formData string true "Message content"
// @Param is_read formData string true "Read status (0/1)"
// @Param is_viewed formData string true "Viewed status (0/1)"
// @Param data_created formData string true "Creation timestamp"
// @Param type formData string true "Message type"
// @Param receiver_id formData string true "Receiver ID"
// @Param admin_fkid formData string true "Admin ID"
// @Param notification_type formData string true "Notification type"
// @Success 200 {object} object "Success response with status"
// @Failure 500 {object} object "Internal server error"
// @Router /v2/add_message [post]
func (mh *MessagingHandler) AddMessage(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		fmt.Printf("form key error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"status":  0,
			"message": err,
		})
	}

	title := form.Value["title"]
	message := form.Value["message"]
	isRead := form.Value["is_read"]
	isViewed := form.Value["is_viewed"]
	dataCreated := form.Value["data_created"]
	types := form.Value["type"]
	receiverID := form.Value["receiver_id"]
	adminFkid := form.Value["admin_fkid"]
	notificationType := form.Value["notification_type"]

	mess := map[string]interface{}{
		"title":             title[0],
		"message":           message[0],
		"is_read":           isRead[0],
		"is_viewed":         isViewed[0],
		"data_created":      dataCreated[0],
		"type":              types[0],
		"receiver_id":       receiverID[0],
		"admin_fkid":        adminFkid[0],
		"notification_type": notificationType[0],
	}
	err = mh.MessagingUseCase.AddMessage(mess)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"status":  0,
			"message": "save message failed",
		})
	}

	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"status":  1,
		"message": "save message success",
	})
}

// FetchMessages handle func
// @Summary Get user messages
// @Description Get all messages for a specific user filtered by type
// @Tags messaging
// @Accept json
// @Produce json
// @Success 200 {array} domain.Message "List of messages"
// @Failure 500 {object} object "Internal server error"
// @Router /v2/fetch_messages [get]
func (mh *MessagingHandler) FetchMessages(c *fiber.Ctx) error {
	// tipe := c.Params("type")
	// emp_id := c.Params("emp_id")
	// id, _ := strconv.Atoi(emp_id)

	user := domain.GetUserSessionFiber(c)
	results, err := mh.MessagingUseCase.FetchMessages(user.UserId, user.UserType)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "fetch messages error",
			"status":  0,
			"error":   err,
		})
	}
	return c.JSON(results)
}

// DeleteNotif handle func
// @Summary Delete a notification
// @Description Delete a specific notification by ID
// @Tags messaging
// @Accept multipart/form-data
// @Produce json
// @Param notif_id formData string true "Notification ID"
// @Success 200 {object} object "Success response with status"
// @Failure 500 {object} object "Internal server error"
// @Router /v2/delete_notif [post]
func (mh *MessagingHandler) DeleteNotif(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		fmt.Printf("get formdata error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "wrong form key",
			"status":  0,
			"error":   err,
		})
	}
	empId := form.Value["notif_id"]
	id, _ := strconv.Atoi(empId[0])
	err = mh.MessagingUseCase.DeleteNotif(id)
	if err != nil {
		fmt.Printf("error geting id: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error getting id",
			"status":  0,
			"error":   err,
		})
	}
	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": "OK",
		"status":  1,
		"error":   nil,
	})
}

// UpdateMessageView handle func
// @Summary Mark messages as viewed
// @Description Update the viewed status of multiple messages
// @Tags messaging
// @Accept multipart/form-data
// @Produce json
// @Param where_id formData string true "Comma-separated list of notification IDs"
// @Success 200 {object} object "Success response with status"
// @Failure 500 {object} object "Internal server error"
// @Router /v2/update_message_view [post]
func (mh *MessagingHandler) UpdateMessageView(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		fmt.Printf("error geting formdata upadate message view: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error geting formdata",
			"error":   err,
		})
	}
	where := form.Value["where_id"]
	whereID := strings.Split(where[0], ",")
	var data []map[string]interface{}
	for _, v := range whereID {
		data = append(data, map[string]interface{}{
			"notification_id": v,
			"is_viewed":       1,
		})
	}
	err = mh.MessagingUseCase.UpdateMessageView(data)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "update view message error",
			"status":  0,
			"error":   err,
		})
	}

	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": "OK",
		"status":  1,
		"error":   err,
	})
}

// UpdateMessageRead handle func
// @Summary Mark messages as read
// @Description Update the read status of multiple messages
// @Tags messaging
// @Accept multipart/form-data
// @Produce json
// @Param where_id formData string true "Comma-separated list of notification IDs"
// @Success 200 {object} object "Success response with status"
// @Failure 500 {object} object "Internal server error"
// @Router /v2/update_message_read [post]
func (mh *MessagingHandler) UpdateMessageRead(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		fmt.Printf("getting form data error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "getting form data error",
			"status":  0,
			"error":   err,
		})
	}
	where := form.Value["where_id"]
	whereID := strings.Split(where[0], ",")
	var data []map[string]interface{}
	for _, v := range whereID {
		data = append(data, map[string]interface{}{
			"notification_id": v,
			"is_read":         1,
		})
	}
	err = mh.MessagingUseCase.UpdateMessageRead(data)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"messgage": "update message read error",
			"status":   0,
			"error":    err,
		})
	}
	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": "success updating message to read",
		"status":  1,
		"error":   err,
	})
}

// DeleteMessages handle func
// @Summary Delete multiple messages
// @Description Delete multiple messages by their IDs
// @Tags messaging
// @Accept multipart/form-data
// @Produce json
// @Param messages_id formData string true "Comma-separated list of message IDs"
// @Success 200 {object} object "Success response with status"
// @Failure 500 {object} object "Internal server error"
// @Router /v2/delete_messages [post]
func (mh *MessagingHandler) DeleteMessages(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "delete messages error",
			"status":  0,
			"error":   err,
		})
	}
	msgID := form.Value["messages_id"]
	arrID := strings.Split(msgID[0], ",")
	err = mh.MessagingUseCase.DeleteMessages(arrID)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "delete messages error",
			"status":  0,
			"error":   err,
		})
	}
	return c.Status(200).JSON(&fiber.Map{
		"message": "delete success",
		"status":  1,
		"error":   nil,
	})
}

// UpdateUserDevice handle func
// @Summary Update user device information
// @Description Update device information for a specific user
// @Tags messaging
// @Accept multipart/form-data
// @Produce json
// @Param user_id formData string true "User ID"
// @Param device formData string true "Device type (android/ios)"
// @Param id formData string true "Device record ID"
// @Success 200 {object} object "Success response with status"
// @Failure 400 {object} object "Bad request error"
// @Router /v2/update_user_device [post]
func (mh *MessagingHandler) UpdateUserDevice(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if log.IfError(err) {
		return c.Status(fiber.ErrBadRequest.Code).JSON(&fiber.Map{
			"message": "form key error",
			"error":   err,
			"status":  fiber.ErrBadRequest.Code,
		})
	}

	userID := form.Value["user_id"][0]
	device := form.Value["device"][0]
	id := form.Value["id"][0]

	data := map[string]interface{}{
		"user_id": userID,
		"device":  device,
	}
	err = mh.MessagingUseCase.UpdateUserDevice(data, id)
	if log.IfError(err) {
		return c.Status(fiber.ErrBadRequest.Code).JSON(&fiber.Map{
			"message": "update user device error",
			"error":   err,
			"status":  fiber.ErrBadRequest.Code,
		})
	}
	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": "update user device success",
		"error":   err,
		"status":  c.Response().StatusCode(),
	})
}
