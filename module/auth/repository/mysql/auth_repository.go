package mysql

import (
	"database/sql"
	"strings"

	log "gitlab.com/backend/api-hrm/core/log"

	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/core/util/cast"
	domain "gitlab.com/backend/api-hrm/domain"
)

type authRepository struct {
	mysql.Repository
}

func AuthRepository(db *sql.DB) domain.AuthRepository {
	return &authRepository{mysql.Repository{Conn: db}}
}

func (r authRepository) FindAccount(userType, email string) (domain.User, string, error) {
	var user domain.User
	var password string
	var err error
	var result map[string]interface{}

	if userType == "admin" {
		result, err = r.Query(`
		SELECT email, password, phone, name,
			admin_id AS user_id,
			admin_id AS business_id,
			'admin' AS user_type,
			'' AS account_id
		FROM admin
		WHERE email=? AND activation_status=?`, email, "activated").PrintSql().Map()
	}
	log.IfError(err)
	if userType == "employee" {
		result, err = r.Query(`
		SELECT email, password, phone, name,
			employee_id AS user_id,
			admin_fkid AS business_id,
			'employee' AS user_type,
			'' AS account_id
		FROM employee
		WHERE email=? AND access_status_web=?`, email, "activated").PrintSql().Map()
	}

	log.IfError(err)
	log.Info("user: %v", result)

	password = cast.ToString(result["password"])
	cast.MapToStruct(result, &user)
	return user, password, err
}

func (r authRepository) GetOutletAccess(user domain.User) (string, error) {
	var result []map[string]interface{}
	var err error
	var outletAccess string

	if user.UserType == "admin" {
		result, err = r.Query("SELECT outlet_id AS id FROM outlets WHERE admin_fkid=? AND data_status=?",
			user.BusinessId, "on").ArrayMap()
	}
	if user.UserType == "employee" {
		result, err = r.Query(`SELECT eo.outlet_fkid AS id, e.admin_fkid AS admin_id
					FROM employee_outlet eo
					LEFT JOIN outlets o ON eo.outlet_fkid=o.outlet_id
					LEFT JOIN employee e ON eo.employee_fkid=e.employee_id
					WHERE o.data_status=? 
					AND o.admin_fkid=?
					AND eo.employee_fkid=?`, "on", user.BusinessId, user.UserId).ArrayMap()
	}

	//generate outlet access
	var outletAccessIds = make([]string, 0)
	for _, data := range result {
		outletAccessIds = append(outletAccessIds, cast.ToString(data["id"]))
	}
	outletAccess = strings.Join(outletAccessIds, ",")

	return outletAccess, err
}
