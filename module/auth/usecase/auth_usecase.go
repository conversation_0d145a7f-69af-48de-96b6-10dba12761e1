package usecase

import (
	"errors"
	"time"

	cf "gitlab.com/backend/api-hrm/config"
	"gitlab.com/backend/api-hrm/core/log"

	"github.com/dgrijalva/jwt-go"
	"golang.org/x/crypto/bcrypt"

	// "gitlab.com/backend/api-hrm/core/util/cast"
	// "gitlab.com/backend/api-hrm/core/util/validation"
	"gitlab.com/backend/api-hrm/domain"
)

type authUseCase struct {
	domain.AuthRepository
	auth cf.JWTAuth
}

func AuthUseCase(repository domain.AuthRepository, auth cf.JWTAuth) domain.AuthUseCase {
	return &authUseCase{repository, auth}
}

func (u authUseCase) CreateToken(lg domain.AuthInputLogin) (domain.UserToken, error) {
	var userToken domain.UserToken

	//validation
	// errList := validation.Struct(lg)
	// if validation.IsError(errList) {
	// 	return userToken, cast.ToError(errList)
	// }

	// fmt.Println("create token :", lg)
	//1. check user from DB
	//-- check user is valid
	user, pass, err := u.AuthRepository.FindAccount("admin", lg.Email)
	// fmt.Println("user admin: ", user)
	if user.UserId == "" {
		user, pass, err = u.AuthRepository.FindAccount("employee", lg.Email)
		// fmt.Println("user employee: ", user)
	}
	if user.UserId == "" {
		//account not found
		log.Info("account not found: %v", lg.Email)
		return userToken, errors.New("404")
	}

	//2. validate user's password
	err = bcrypt.CompareHashAndPassword([]byte(pass), []byte(lg.Password))
	if err != nil {
		//invalid email or password
		log.Info("invalid email or password: %v", lg.Email)
		return userToken, errors.New("401")
	}

	//3. if valid, get user's outlet
	outletAccess, _ := u.AuthRepository.GetOutletAccess(user)

	//4. then create token payload
	token := jwt.New(u.auth.Method)
	expired := time.Now().Add(time.Hour * 72).Unix()
	claims := token.Claims.(jwt.MapClaims)
	claims["exp"] = expired
	claims["role"] = map[string]interface{}{
		"outlet_access": outletAccess,
	}
	claims["data"] = user

	// Generate encoded token and send it as response.
	// key := *u.auth.PrivateKey
	generatedToken, err := token.SignedString(u.auth.PrivateKey)

	//prepare output
	userToken.User = user
	userToken.UserRole = domain.UserRole{OutletAccess: outletAccess}
	userToken.Token = domain.Token{
		Token:   generatedToken,
		Expired: expired,
		Type:    "Bearer",
	}

	return userToken, err
}
