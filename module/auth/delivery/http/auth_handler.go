package http

import (
	"github.com/gofiber/fiber/v2"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/cast"
	domain "gitlab.com/backend/api-hrm/domain"
)

type authHandler struct {
	domain.AuthUseCase
}

// <PERSON><PERSON><PERSON><PERSON><PERSON> initializes the authentication routes
// @Summary Initialize authentication routes
// @Description Set up authentication related endpoints
func AuthHandler(app *fiber.App, useCase domain.AuthUseCase) {
	handler := &authHandler{useCase}
	app.Post("/auth/login", handler.Login)
}

// Login godoc
// @Summary Login to the system
// @Description Authenticate user and return access token
// @Tags auth
// @Accept json
// @Produce json
// @Param input body domain.AuthInputLogin true "Login credentials"
// @Success 200 {object} domain.UserToken "Successfully authenticated with user details and token"
// @Failure 400 {object} error "Bad request - Invalid input"
// @Failure 401 {object} error "Unauthorized - Invalid email or password"
// @Failure 404 {object} error "User not found"
// @Failure 422 {object} error "Unprocessable entity"
// @Router /auth/login [post]
func (h authHandler) Login(c *fiber.Ctx) error {
	var inputLogin domain.AuthInputLogin
	log.IfError(c.BodyParser(&inputLogin))

	// process create token
	user, err := h.AuthUseCase.CreateToken(inputLogin)
	if err != nil {
		errStr := cast.ToString(err)
		switch errStr {
		case "401":
			return c.SendStatus(401)
		case "404":
			return c.SendStatus(404)
		default:
			return c.SendStatus(422)
		}
	}

	return c.JSON(user)
}
