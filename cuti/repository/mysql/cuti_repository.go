package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	log "gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/cast"

	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLCutiRepository struct {
	mysql.Repository
}

// NewMySQLCutiRepository func
func NewMySQLCutiRepository(conn *sql.DB) domain.CutiRepository {
	return &mySQLCutiRepository{mysql.Repository{Conn: conn}}
}

// fetch all data repository
func (m *mySQLCutiRepository) Fetch(adminID int) ([]domain.CutiDetail, error) {
	// Updated query to use hrm_employee table directly without employee_fkid joins
	query := "SELECT trc.trc_id AS TRC_ID, trc.outlet_fkid, dtrc.dtrc_reason AS REASON, trc.hrm_employee_fkid AS EMPLOYEE_ID, DATE_FORMAT(trc.trc_date_start, '%d-%m-%Y') AS START_DATE, DATE_FORMAT(trc.trc_date_end, '%d-%m-%Y') AS END_DATE, trc.trc_reason AS DESCR, DATE_FORMAT( trc.trc_created, '%d-%m-%Y %H:%i:%s' ) AS CREATED, emp.nik AS NIK, emp.name AS EMPLOYEE_NAME, emp.address AS ADDRESS, emp.phone AS PHONE, otl.name AS OUTLET, CASE WHEN trc.trc_status = '0' THEN 'Pending' WHEN trc.trc_status = '1' THEN 'Approved all' WHEN trc.trc_status = '3' THEN 'Rejected' WHEN trc.trc_status = '2' THEN 'Partially Approved' ELSE 'Canceled' END AS STATUS , trc.trc_id AS ACTION FROM hrm_trans_cuti AS trc INNER JOIN hrm_employee AS emp ON emp.hrm_employee_id = trc.hrm_employee_fkid AND emp.data_status = 'on' INNER JOIN hrm_detail_trans_cuti AS dtrc ON dtrc.trc_id = trc.trc_id INNER JOIN outlets AS otl ON otl.outlet_id = trc.outlet_fkid WHERE otl.admin_fkid = ? ORDER BY trc.trc_created DESC"
	cuti, err := m.QueryArrayOld(query, adminID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resultJSON, err := json.Marshal(cuti)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var results []domain.CutiDetail
	var newResult []domain.CutiDetail
	err = json.Unmarshal(resultJSON, &results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	for k, v := range results {
		if v.NO == 0 {
			v.NO = k + 1
			newResult = append(newResult, v)
		}
	}
	return newResult, nil
}

// AddCuti repository
func (m *mySQLCutiRepository) AddCuti(cuti map[string]any) (int64, error) {
	_, id, err := m.InsertGetLastID("hrm_trans_cuti", cuti)
	if err != nil {
		log.IfError(err)
		return 0, err
	}
	return id, nil
}

// AddCutiV2 implements domain.CutiRepository.
func (m *mySQLCutiRepository) AddCutiV2(param domain.AddCutiParam) error {
	loc, err := time.LoadLocation("Asia/Jakarta")
	if log.IfError(err) {
		return err
	}
	err = m.WithTransaction(func(tx mysql.Transaction) error {
		resp := tx.Insert("hrm_trans_cuti", param.AddCutiRequest.ToMap())
		trcId, _ := resp.LastInsertId()
		log.Info("hrm_trans_cuti added, id: %v", trcId)

		//add attachment if any
		if len(param.AddCutiRequest.Attachments) > 0 {
			for _, v := range param.AddCutiRequest.Attachments {
				tx.Insert("hrm_cuti_attachment", map[string]any{
					"trc_id":            trcId,
					"hrm_employee_fkid": param.AddCutiRequest.HrmEmployeeID,
					"file":              v,
				})
			}
		}

		var userId any
		var adminId any
		if param.AddCutiRequest.UserType == "employee" {
			userId = param.AddCutiRequest.HrmEmployeeID
		} else if param.AddCutiRequest.UserType == "admin" {
			adminId = param.AddCutiRequest.AdminFkid
		}

		//add to hrm_detail_trans_cuti
		dates := cast.DateRanges((param.AddCutiRequest.TrcDateStart), (param.AddCutiRequest.TrcDateEnd))
		log.Info("date from %v to %v : %v (%v items)", param.AddCutiRequest.TrcDateStart, param.AddCutiRequest.TrcDateEnd, cast.ToString(dates), len(dates))
		for _, v := range dates {
			resp = tx.Insert("hrm_detail_trans_cuti", map[string]any{
				"trc_id":            trcId,
				"outlet_fkid":       param.AddCutiRequest.OutletFkid,
				"hrm_employee_fkid": param.AddCutiRequest.HrmEmployeeID,
				"dtrc_date":         v,
				"dtrc_status":       "0",
				"type":              param.AddCutiRequest.Type,
				"dtrc_date_updated": time.Now().In(loc).Format("2006-01-02 15:04:05"),
			})

			transCutiId, _ := resp.LastInsertId()
			detailsRecord := map[string]any{
				"dtrc_fkid":     trcId,
				"dtrc_id":       transCutiId,
				"user":          userId,
				"admin":         adminId,
				"email":         "",
				"date_cuti":     v,
				"from_status":   "0",
				"to_status":     nil,
				"reason_status": nil,
				"date_updated":  time.Now().In(loc).Format("2006-01-02 15:04:05"),
			}
			tx.Insert("hrm_record_cuti", detailsRecord)
		}

		//add to hrm_detail_attachment
		detailAttach := map[string]any{
			"trc_fkid":    trcId,
			"status":      "0",
			"date_update": time.Now().In(loc).Format("2006-01-02 15:04:05"),
		}
		resp = tx.Insert("hrm_detail_attachment", detailAttach)
		daid, _ := resp.LastInsertId()

		//add to hrm_record_cuti
		record := map[string]any{
			"dtrc_fkid":     trcId,
			"dtrc_id":       daid,
			"user":          userId,
			"admin":         adminId,
			"email":         "",
			"date_cuti":     nil,
			"from_status":   "0",
			"to_status":     nil,
			"reason_status": nil,
			"date_updated":  time.Now().In(loc).Format("2006-01-02 15:04:05"),
		}
		tx.Insert("hrm_record_cuti", record)
		return nil
	})
	return err
}

// AddAttch repository
func (m *mySQLCutiRepository) AddAttch(attachment []map[string]any) error {
	_, err := m.BulkInsert("hrm_cuti_attachment", attachment)
	if err != nil {
		log.IfError(err)
		return err
	}
	return nil
}

// AddDetailTransCuti repository
func (m *mySQLCutiRepository) AddDetailTransCuti(details map[string]any) (int64, error) {
	_, id, err := m.InsertGetLastID("hrm_detail_trans_cuti", details)
	if err != nil {
		log.IfError(err)
		return 0, err
	}
	return id, err
}

// AddDetailAttch repository
func (m *mySQLCutiRepository) AddDetailAttch(dtAttach map[string]any) (int64, error) {
	_, id, err := m.InsertGetLastID("hrm_detail_attachment", dtAttach)
	if err != nil {
		log.IfError(err)
		return 0, err
	}
	return id, err
}

// AddRecordCuti repository
func (m *mySQLCutiRepository) AddRecordCuti(record map[string]any) error {
	_, _, err := m.InsertGetLastID("hrm_record_cuti", record)
	if err != nil {
		log.IfError(err)
		return err
	}
	return err
}

// FetchDayOffRemains repository
func (m *mySQLCutiRepository) FetchDayOffRemains(empID int) (domain.DayOffRemains, error) {
	var result domain.DayOffRemains
	query := "SELECT((SELECT max_leave FROM hrm_employee WHERE hrm_employee_id=?) - IFNULL(COUNT(*), 0)) AS SISA_CUTI FROM hrm_detail_trans_cuti AS dtc INNER JOIN hrm_trans_cuti AS trc ON trc.trc_id = dtc.trc_id WHERE trc.hrm_employee_fkid=? AND dtc.dtrc_status = 1"
	err := m.Query(query, empID, empID).Model(&result)
	if err != nil {
		log.IfError(err)
		return domain.DayOffRemains{}, err
	}
	return result, nil
}

// FetchDetailsCuti repository
func (m *mySQLCutiRepository) FetchDetailsCuti(trcID, adminID int) ([]domain.DetailsCuti, error) {
	query := `SELECT dtc.dtrc_id,
	dtc.trc_id, DATE_FORMAT(dtc.dtrc_date, '%d-%m-%Y') AS dtrc_date,
	DATE_FORMAT(trc.trc_date_start, '%d-%m-%Y') AS date_start,
	DATE_FORMAT(trc.trc_date_end, '%d-%m-%Y') AS date_end,
	emp.hrm_employee_id AS employee_fkid, emp.nik, emp.name, emp.address,
	emp.phone, emp.email, otl.name AS outlet, dtc.dtrc_status,
	dtc.type, dtc.dtrc_reason AS reason_cuti, trc.trc_status AS status_cuti,
	trc.trc_reason AS status_reason, trc.outlet_fkid
	FROM hrm_detail_trans_cuti AS dtc
	INNER JOIN hrm_trans_cuti AS trc ON trc.trc_id = dtc.trc_id
	INNER JOIN hrm_employee AS emp ON emp.hrm_employee_id = trc.hrm_employee_fkid
	INNER JOIN outlets AS otl ON otl.outlet_id = trc.outlet_fkid
	WHERE dtc.trc_id=? AND emp.admin_fkid=?
	ORDER BY DATE_FORMAT(dtc.dtrc_date, '%Y-%m-%d') ASC`
	result, err := m.QueryArrayOld(query, trcID, adminID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resultJSON, err := json.Marshal(result)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var results []domain.DetailsCuti
	err = json.Unmarshal(resultJSON, &results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	return results, nil
}

// FetchAttach repository
func (m *mySQLCutiRepository) FetchAttach(trcID int) ([]domain.Attachment, error) {
	query := "SELECT attach_id, trc_id, file FROM hrm_cuti_attachment WHERE trc_id=?"
	attach, err := m.QueryArrayOld(query, trcID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resultJSON, err := json.Marshal(attach)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var results []domain.Attachment
	err = json.Unmarshal(resultJSON, &results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	return results, err
}

// FetchDetailsAttch repository
func (m *mySQLCutiRepository) FetchDetailsAttch(trcID int) ([]domain.DetailsAttach, error) {
	query := "SELECT da_id, status, attach_reason FROM hrm_detail_attachment WHERE trc_fkid=?"
	DA, err := m.QueryArrayOld(query, trcID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resultJSON, err := json.Marshal(DA)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var result []domain.DetailsAttach
	err = json.Unmarshal(resultJSON, &result)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	return result, err
}

// FetchRecord repository
func (m *mySQLCutiRepository) FetchRecord(trcID int) ([]domain.Record, error) {
	query := "SELECT dtrc_fkid, dtrc_id, user, admin, email, date_cuti, from_status, to_status, reason_status, date_updated FROM hrm_record_cuti WHERE dtrc_fkid=?"
	record, err := m.QueryArrayOld(query, trcID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resultJSON, err := json.Marshal(record)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var result []domain.Record
	err = json.Unmarshal(resultJSON, &result)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	return result, err
}

// FetchEmployee respository
func (m *mySQLCutiRepository) FetchEmployee() ([]domain.CutiEmployee, error) {
	query := "SELECT hrm_employee_id AS id, name FROM hrm_employee WHERE data_status = 'on'"
	emp, err := m.QueryArrayOld(query)
	if err != nil {
		log.IfError(err)
		return []domain.CutiEmployee{}, err
	}
	resJSON, err := json.Marshal(emp)
	if err != nil {
		log.IfError(err)
		return []domain.CutiEmployee{}, err
	}
	var results []domain.CutiEmployee
	err = json.Unmarshal(resJSON, &results)
	if err != nil {
		log.IfError(err)
		return []domain.CutiEmployee{}, err
	}
	return results, err
}

// FetchAdmin respository
func (m *mySQLCutiRepository) FetchAdmin() ([]domain.Admin, error) {
	query := "SELECT admin_id, email, name FROM admin"
	adm, err := m.QueryArrayOld(query)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resJSON, err := json.Marshal(adm)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var results []domain.Admin
	err = json.Unmarshal(resJSON, &results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	return results, err
}

// SaveDetailCuti repository
func (m *mySQLCutiRepository) SaveDetailCuti(cuti domain.SaveDetailCutiRequest) error {
	// get location now
	loc, _ := time.LoadLocation("Asia/Jakarta")
	// get time now
	timeNow := time.Now().In(loc).Format("2006-01-02 15:04:05")

	var status []map[string]any
	var key []string
	var stat []int

	for _, w := range cuti.Keys {
		for _, x := range w {
			key = append(key, x.(string))
		}
	}

	for _, w := range cuti.Status {
		status = append(status, w)
		for _, v := range w {
			if v.(string) != "" {
				val, err := strconv.Atoi(v.(string))
				log.IfError(err)
				stat = append(stat, val)
			} else {
				stat = append(stat, 0)
			}
		}
	}

	// save document
	var dataAttach []map[string]any
	if len(cuti.Documents) != 0 {
		for _, v := range cuti.Documents {
			att := map[string]any{
				"trc_id":          cuti.TrcID,
				"hrm_employee_id": cuti.EmpID,
				"file":            v,
			}
			dataAttach = append(dataAttach, att)
		}
	}

	var dataInsertTip []map[string]any
	var dataUpdateDtrc []map[string]any
	var dataUpdateAttach []map[string]any
	var dataInsertRecord []map[string]any
	var dataUpdateTrc []map[string]any

	log.Info("status: %v", cast.ToString(status))
	for p, v := range status {
		for k, l := range v {
			if key[p] == k {
				if cuti.Date[p][key[p]] != "null" {
					if l.(string) == "1" {
						for i := 1; i < 7; i++ {
							dataDel := map[string]any{
								"tip_nik":       cuti.Nik,
								"tip_outlet_id": cuti.OutletID,
								"tip_tanggal":   cuti.Date[p][k],
							}
							log.Info("remove hrm_trans_import_presensi: %v", cast.ToString(dataDel))

							dataInsert := map[string]any{
								"tip_outlet_id":     cuti.OutletID,
								"tip_nik":           cuti.Nik,
								"tip_nama_karyawan": cuti.EmpName,
								"tip_tanggal":       cuti.Date[p][key[p]],
								"tip_jam":           "00:00:00",
								"employee_id":       cuti.EmpID,
								"tip_kode":          i,
								"tip_hash":          cast.HashS(cuti.Date[p][k].(string))[0:15] + cast.HashS(strconv.Itoa((p + i)))[0:15],
							}
							dataInsertTip = append(dataInsertTip, dataInsert)
						}
					}
					dataDelete := map[string]any{
						"tip_nik":       cuti.Nik,
						"tip_outlet_id": cuti.OutletID,
						"tip_tanggal":   cuti.Date[p][key[p]],
					}
					log.Info("remove hrm_trans_import_presensi: %v", cast.ToString(dataDelete))
				}
			}

			// data update detail_trans_cuti
			var reasons string
			var dtrcID string

			if l.(string) == "" {
				l = "0"
			}
			if len(cuti.Reasons) != 0 && l.(string) == "2" {
				for i := range cuti.Reasons {
					for u, v := range cuti.Reasons[i] {
						if u == k {
							reasons = v.([]string)[0]
							dtrcID = k
						}
					}
				}
			} else {
				reasons = ""
				dtrcID = k
			}
			dataUpdateDtrc = append(dataUpdateDtrc, map[string]any{
				"dtrc_status": l,
				"dtrc_reason": reasons,
				"dtrc_id":     dtrcID,
			})

			// data update hrm_detail_attachment
			var attachReason string
			var aDtrcID string
			if cuti.TrcID == k {
				if len(cuti.Reasons) != 0 && l.(string) == "2" {
					for i := range cuti.Reasons {
						for u, v := range cuti.Reasons[i] {
							if u == k {
								attachReason = v.([]string)[0]
								aDtrcID = k
							}
						}
					}
				} else {
					attachReason = ""
					aDtrcID = k
				}

				dataUpdateAttach = append(dataUpdateAttach, map[string]any{
					"status":        l,
					"attach_reason": attachReason,
					"trc_fkid":      aDtrcID,
				})
			}

			var reasonStatus string
			var valAdmin string
			var valUser string
			var valEmail string
			j := 0
			if key[p] == k {
				if l.(string) != cuti.StatusInfo[p][key[p]] {
					if len(cuti.Reasons) != 0 && l == "2" && cuti.Reasons[j][key[p]] != nil {
						reasonStatus = cuti.Reasons[j][key[p]].([]string)[0]
						j = j + 1
					} else {
						reasonStatus = ""
					}

					if cuti.Type == "employee" {
						valAdmin = "0"
						valUser = cuti.User
						valEmail = cuti.EmailAdmin
					} else {
						valAdmin = cuti.Admin
						valUser = "0"
						valEmail = cuti.EmailAdmin
					}

					if cuti.Date[p][key[p]] == "null" {
						cuti.Date[p][key[p]] = nil
					}
					dataInsertRecord = append(dataInsertRecord, map[string]any{
						"to_status":     l,
						"reason_status": reasonStatus,
						"dtrc_fkid":     cuti.TrcID,
						"dtrc_id":       k,
						"admin":         valAdmin,
						"user":          valUser,
						"email":         valEmail,
						"from_status":   cuti.Status[p][key[p]],
						"date_cuti":     cuti.Date[p][key[p]],
						"date_updated":  timeNow,
					})
				}
			}
		}
	}

	lenStatus := cast.ArrayLenValue(stat)
	lenData := len(status)

	var statusApproved int
	log.Info("lendata: %v, statuses: %v", lenData, lenStatus)
	//0: pending, 1: all approved, 2: partially approved, 3: rejected, 4: canceled
	if lenStatus[0] == lenData {
		statusApproved = 0
	} else if lenStatus[1] == lenData {
		statusApproved = 1
	} else if lenStatus[2] == lenData {
		statusApproved = 3
	} else if lenStatus[3] == lenData {
		statusApproved = 4
	} else {
		statusApproved = 2
	}

	log.Info("status approved: %v \n", statusApproved)

	dataUpTrc := map[string]any{
		"trc_id":      cuti.TrcID,
		"trc_status":  statusApproved,
		"trc_updated": timeNow,
	}
	dataUpdateTrc = append(dataUpdateTrc, dataUpTrc)

	// Execute all operations in a transaction
	err := m.WithTransaction(func(tx mysql.Transaction) error {
		// 1. Insert attachments if any
		tx.BulkInsert("hrm_cuti_attachment", dataAttach)

		// 2. Delete and insert trans import presensi
		log.Info("status: %v", cast.ToString(status))
		for p, v := range status { //e.g: [{"666":"2"},{"1049":"1"},{"1050":"1"}] | key: id, value: status
			// log.Info("v: %v, p: %v", cast.ToString(v), p)
			for k, _ := range v {
				if key[p] == k && cuti.Date[p][key[p]] != "null" {
					// Delete existing records
					tx.Delete("hrm_trans_import_presensi", map[string]any{
						"tip_nik":       cuti.Nik,
						"tip_outlet_id": cuti.OutletID,
						"tip_tanggal":   cuti.Date[p][k],
					})
				}
			}
		}

		tx.BulkInsert("hrm_trans_import_presensi", dataInsertTip)

		// 3. Update detail attachment
		for _, att := range dataUpdateAttach {
			tx.Update("hrm_detail_attachment", att, "trc_fkid = ?", att["trc_fkid"])
		}

		// 4. Update detail trans cuti
		for _, dtrc := range dataUpdateDtrc {
			tx.Update("hrm_detail_trans_cuti", dtrc, "dtrc_id = ?", dtrc["dtrc_id"])
		}

		// 5. Insert record cuti
		tx.BulkInsert("hrm_record_cuti", dataInsertRecord)

		// 6. Update trans cuti
		for _, trc := range dataUpdateTrc {
			tx.Update("hrm_trans_cuti", trc, "trc_id = ?", trc["trc_id"])
		}

		return nil
	})

	log.IfError(err)
	return err
}

// CancelCuti
func (m *mySQLCutiRepository) CancelCuti(data map[string]any) error {
	period := cast.DateRanges(cast.DateReverse(data["date_start"].(string)), cast.DateReverse(data["date_end"].(string)))
	for _, v := range period {
		where := map[string]any{
			"employee_id":   data["emp_id"],
			"tip_outlet_id": data["outlet_id"],
			"tip_tanggal":   v,
		}
		// deleting hrm_tran_import_presensi
		del, err := m.Deletes("hrm_trans_import_presensi", where)
		if err != nil {
			log.IfError(err)
			return err
		}
		num, err := del.RowsAffected()
		if err != nil {
			log.IfError(err)
			return err
		}
		if num != 0 {
			where = map[string]any{}
		}
	}

	// updating hrm_details_trans_cuti
	dtUp := map[string]any{
		"dtrc_status": 3,
	}
	upWhere := map[string]any{
		"trc_id": data["trc_id"],
	}

	// updating hrm_details_trans_cuti
	res, err := m.Updates("hrm_detail_trans_cuti", dtUp, upWhere)
	if err != nil {
		log.IfError(err)
		return err
	}
	num, err := res.RowsAffected()
	if err != nil {
		log.IfError(err)
		return err
	}

	if num != 0 {
		// updating hrm_trans_cuti
		dUp := map[string]any{
			"trc_status": 4,
		}
		uWhere := map[string]any{
			"trc_id": data["trc_id"],
		}
		// updating hrm_trans_cuti
		_, err = m.Updates("hrm_trans_cuti", dUp, uWhere)
		if err != nil {
			log.IfError(err)
			return err
		}
	}
	return nil
}

// DeleteCuti repository
func (m *mySQLCutiRepository) DeleteCuti(data map[string]any) error {
	// get date range
	periode := cast.DateRanges(cast.DateReverse(data["start_date"].(string)), cast.DateReverse(data["end_date"].(string)))

	err := m.WithTransaction(func(tx mysql.Transaction) error {
		// map where for delete
		where := map[string]any{
			"trc_id": data["trc_id"],
		}

		// Delete from hrm_detail_trans_cuti
		tx.Delete("hrm_detail_trans_cuti", where)
		log.Info("remove hrm_detail_trans_cuti, & hrm_trans_cuti: %v ", cast.ToString(where))
		tx.Delete("hrm_trans_cuti", where)

		// Delete from hrm_trans_import_presensi for each date in period
		log.Info("will delete these periods: %v", cast.ToString(periode))
		for _, v := range periode {
			where := map[string]any{
				"tip_outlet_id": data["outlet_id"],
				"employee_id":   data["emp_id"],
				"tip_tanggal":   v,
			}
			tx.Delete("hrm_trans_import_presensi", where)
			log.Info("delete hrm_trans_import_presensi: %v", cast.ToString(where))
		}
		return nil
	})

	log.IfError(err)
	return err
}

func (m *mySQLCutiRepository) FetchEmployeeOutlet(empID int) ([]domain.EmployeeOutletID, error) {
	// Updated to get outlet from hrm_employee table directly since employee_outlet table may not exist
	query := "SELECT outlet_fkid FROM hrm_employee WHERE hrm_employee_id=?"
	result, err := m.QueryArrayOld(query, empID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resJSON, err := json.Marshal(result)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var results []domain.EmployeeOutletID
	err = json.Unmarshal(resJSON, &results)
	if err != nil {
		log.IfError(err)
		return []domain.EmployeeOutletID{}, err
	}
	return results, err
}

func (m *mySQLCutiRepository) FetchShift(outletID int, shiftCode string) ([]domain.ShiftTime, error) {

	query := "SELECT hms.shift_in, hms.shift_out, hms.shift_tolerance, hms.shift_type, het.type_name, het.type_hours, het.rest_hours FROM hrm_master_shift AS hms JOIN hrm_employee_type AS het ON het.type_id=hms.shift_type WHERE shift_office=? AND shift_code=?"
	result, err := m.QueryArrayOld(query, outletID, shiftCode)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resJSON, err := json.Marshal(result)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var results []domain.ShiftTime
	err = json.Unmarshal(resJSON, &results)
	if err != nil {
		log.IfError(err)
		return []domain.ShiftTime{}, err
	}
	return results, err
}

func (m *mySQLCutiRepository) FetchEmployeeShift(empID int, outletID int, date string) ([]domain.EmployeeShift, error) {
	query := "SELECT shift_id, outlet_id FROM hrm_trans_schedule WHERE hrm_employee_fkid=? AND schedule_date=? AND outlet_id=?"
	result, err := m.QueryArrayOld(query, empID, date, outletID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resJSON, err := json.Marshal(result)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var results []domain.EmployeeShift
	err = json.Unmarshal(resJSON, &results)
	if err != nil {
		return []domain.EmployeeShift{}, err
	}
	return results, err
}

func (m *mySQLCutiRepository) FetchEmployeeCutiDetail(empID int) ([]domain.EmployeeCutiDetail, error) {
	query := "SELECT trc_id, trc_date_start, trc_date_end, trc_reason, trc_status, type FROM hrm_trans_cuti WHERE hrm_employee_fkid=?"
	result, err := m.QueryArrayOld(query, empID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resJSON, err := json.Marshal(result)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var results []domain.EmployeeCutiDetail
	err = json.Unmarshal(resJSON, &results)
	if err != nil {
		log.IfError(err)
		return []domain.EmployeeCutiDetail{}, err
	}
	return results, nil
}

func (m *mySQLCutiRepository) FetchEmpRole(empID int) ([]domain.EmpRole, error) {
	query := "SELECT ej.name AS jabatan, emp.name AS emp_name FROM hrm_employee AS emp JOIN employees_jabatan AS ej ON ej.jabatan_id=emp.jabatan_fkid WHERE emp.hrm_employee_id=?"
	result, err := m.QueryArrayOld(query, empID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resJSON, err := json.Marshal(result)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var results []domain.EmpRole
	err = json.Unmarshal(resJSON, &results)
	if err != nil {
		log.IfError(err)
		return []domain.EmpRole{}, err
	}
	return results, err
}

func (m *mySQLCutiRepository) FetchAttendance(outletID int, date string, empID int) ([]domain.Attendance, error) {
	args := []any{}
	args = append(args, outletID, empID, date)
	query := `
	SELECT tip_jam, tip_kode 
	FROM hrm_trans_import_presensi 
	JOIN hrm_employee ON tip_nik=nik
	WHERE tip_outlet_id=? 
	AND hrm_employee_id=? 
	AND tip_tanggal=DATE_FORMAT(?, '%Y-%m-%d')
	`
	result, err := m.QueryArrayOld(query, args...)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resJSON, err := json.Marshal(result)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var results []domain.Attendance
	err = json.Unmarshal(resJSON, &results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	return results, nil
}

func (m *mySQLCutiRepository) Absensi(data map[string]any) error {
	type employeeNik struct {
		Nik string `json:"nik"`
	}
	query := "SELECT nik FROM hrm_employee WHERE hrm_employee_id=?"
	nik, err := m.QueryArrayOld(query, data["employee_id"])
	if err != nil {
		log.IfError(err)
		return err
	}
	resultJSON, err := json.Marshal(nik)
	if err != nil {
		log.IfError(err)
		return err
	}
	var result []employeeNik
	err = json.Unmarshal(resultJSON, &result)
	if err != nil {
		log.IfError(err)
		return err
	}

	hash := cast.HashS(data["tip_tanggal"].(string))[0:5] + cast.HashS(data["tip_jam"].(string))[0:5] + cast.HashS(data["employee_id"].(string))[0:5] + cast.HashS(data["tip_kode"].(string))[0:10] + cast.HashS(data["tip_outlet_id"].(string))[0:5]
	data["tip_hash"] = hash
	data["tip_nik"] = result[0].Nik
	_, _, err = m.InsertGetLastID("hrm_trans_import_presensi", data)
	if err != nil {
		log.IfError(err)
		return err
	}
	return err
}

func (m *mySQLCutiRepository) FetchCutiDetails(trcID int) ([]domain.DetailsCutiM, error) {
	query := `SELECT DATE_FORMAT(dtc.dtrc_date, '%d-%m-%Y') AS dtrc_date, DATE_FORMAT(trc.trc_date_start, '%d-%m-%Y') AS date_start,
	 DATE_FORMAT(trc.trc_date_end, '%d-%m-%Y') AS date_end, dtc.dtrc_reason AS reason_cuti, trc.trc_status AS status_cuti,
	 trc.trc_reason AS status_reason, trc.outlet_fkid
	 FROM hrm_detail_trans_cuti AS dtc
	 INNER JOIN hrm_trans_cuti AS trc ON trc.trc_id = dtc.trc_id
	 INNER JOIN hrm_employee AS emp ON emp.hrm_employee_id = trc.hrm_employee_fkid
	 INNER JOIN outlets AS otl ON otl.outlet_id = trc.outlet_fkid
	 WHERE dtc.trc_id = ?
	 ORDER BY DATE_FORMAT(dtc.dtrc_date, '%Y-%m-%d') DESC`
	res, err := m.QueryArrayOld(query, trcID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resultJSON, err := json.Marshal(res)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var results []domain.DetailsCutiM
	err = json.Unmarshal(resultJSON, &results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	return results, err
}

func (m *mySQLCutiRepository) FetchOutletLatLing(outletID []string) ([]domain.OutletLatLing, error) {
	query := "SELECT outlet_id, name, address, latitude, longitude, outlet_logo FROM outlets WHERE outlet_id IN("
	var args []any

	if len(outletID) > 0 {
		for _, v := range outletID {
			query += "?,"
			args = append(args, v)
		}
		query = query[:len(query)-1] + ")"
	}
	res, err := m.QueryArrayOld(query, args...)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resJSON, err := json.Marshal(res)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var results []domain.OutletLatLing
	err = json.Unmarshal(resJSON, &results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	return results, err
}

func (m *mySQLCutiRepository) FetchEmployeeDetails(employeeID int) ([]domain.EmployeeDetails, error) {
	query := "SELECT name, profile_img, photo, address, phone, email, nik, hrm_employee_id AS employee_id FROM hrm_employee WHERE hrm_employee_id=? AND data_status = 'on'"
	res, err := m.QueryArrayOld(query, employeeID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resJSON, err := json.Marshal(res)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var results []domain.EmployeeDetails
	err = json.Unmarshal(resJSON, &results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	return results, err
}

func (m *mySQLCutiRepository) FetchHrmEmpID(employeeID int) (domain.HrmEmpID, error) {
	var result domain.HrmEmpID
	err := m.Query("SELECT * FROM hrm_employee WHERE hrm_employee_id=?", employeeID).Model(&result)
	if err != nil {
		log.IfError(err)
		return domain.HrmEmpID{}, err
	}
	return result, err
}

func (m *mySQLCutiRepository) GetUserDeviceToken(empID int) ([]domain.DeviceToken, error) {
	query := "SELECT token FROM user_notification_token WHERE user_id=? AND user_type='employee'"
	res, err := m.QueryArrayOld(query, empID)
	if err != nil {
		return nil, err
	}
	resJSON, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	var results []domain.DeviceToken
	err = json.Unmarshal(resJSON, &results)
	return results, err
}

func (m *mySQLCutiRepository) SaveMessage(data map[string]any) (int64, error) {
	_, id, err := m.InsertGetLastID("system_notification", data)
	if err != nil {
		log.IfError(err)
		fmt.Printf("error inserting message: %v", err)
		return 0, err
	}
	return id, nil
}

func (m *mySQLCutiRepository) FetchFile(trcID int) ([]domain.FileCuti, error) {
	query := "SELECT file FROM `hrm_cuti_attachment` WHERE trc_id=?"
	res, err := m.QueryArrayOld(query, trcID)
	if err != nil {
		log.IfError(err)
		fmt.Printf("query error: %v", err)
		return nil, err
	}
	resJSON, err := json.Marshal(res)
	if err != nil {
		log.IfError(err)
		fmt.Printf("marshalling json error: %v", err)
		return nil, err
	}
	var results []domain.FileCuti
	err = json.Unmarshal(resJSON, &results)
	return results, err
}

func (m *mySQLCutiRepository) FetchEmpType(empID int) ([]domain.EmpType, error) {
	query := "SELECT he.type_fkid,het.type_name, het.type_hours, het.rest_hours FROM hrm_employee AS he JOIN hrm_employee_type AS het ON het.type_id=he.type_fkid WHERE he.hrm_employee_id=?"
	res, err := m.QueryArrayOld(query, empID)
	if err != nil {
		log.IfError(err)
		fmt.Printf("query fetch employee type error: %v", err)
		return nil, err
	}
	resJSON, err := json.Marshal(res)
	if err != nil {
		log.IfError(err)
		fmt.Printf("marshalling employee type error: %v", err)
		return nil, err
	}
	var results []domain.EmpType
	err = json.Unmarshal(resJSON, &results)
	return results, err
}

func (m *mySQLCutiRepository) FetchSingleEmployeeCuti(trcID int) (domain.EmployeeCutiDetail, error) {
	query := "SELECT trc_id, trc_date_start, trc_date_end, trc_reason, trc_status, type FROM hrm_trans_cuti WHERE trc_id=?"
	var result domain.EmployeeCutiDetail
	err := m.Query(query, trcID).Model(&result)
	if err != nil {
		log.IfError(err)
		return domain.EmployeeCutiDetail{}, err
	}
	return result, err
}

// FetchCutiV2 repository
func (m *mySQLCutiRepository) FetchCutiV2(adminID int, filter domain.CutiFilter) ([]domain.CutiV2Detail, error) {
	query := `
		SELECT 
			htc.trc_id AS trc_id,
			htc.outlet_fkid,
			htc.hrm_employee_fkid AS employee_id,
			DATE_FORMAT(htc.trc_date_start, '%d-%m-%Y') AS start_date,
			DATE_FORMAT(htc.trc_date_end, '%d-%m-%Y') AS end_date,
			htc.trc_reason AS descr,
			DATE_FORMAT(htc.trc_created, '%d-%m-%Y %H:%i:%s') AS created,
			he.nik,
			he.name AS employee_name,
			he.address,
			he.phone,
			o.name AS outlet,
			GROUP_CONCAT(hrc.reason_status) AS reason,
			CASE 
				WHEN htc.trc_status = '0' THEN 'Pending'
				WHEN htc.trc_status = '1' THEN 'Approved'
				WHEN htc.trc_status = '2' THEN 'Partially Approved'
				WHEN htc.trc_status = '3' THEN 'Rejected'
				WHEN htc.trc_status = '4' THEN 'Canceled'
				ELSE 'Pending'
			END AS status,
			htc.trc_id AS action
		FROM hrm_trans_cuti htc
		JOIN hrm_employee he ON he.hrm_employee_id = htc.hrm_employee_fkid
		JOIN outlets o ON o.outlet_id = htc.outlet_fkid
		LEFT JOIN hrm_record_cuti hrc ON hrc.dtrc_fkid = htc.trc_id
		WHERE o.admin_fkid = @adminId
		$WHERE
		GROUP BY htc.trc_id, htc.outlet_fkid, htc.hrm_employee_fkid, htc.trc_date_start, htc.trc_date_end, 
			htc.trc_reason, htc.trc_created, he.nik, he.name, he.address, he.phone, o.name, htc.trc_status
		ORDER BY htc.trc_created DESC`

	var where strings.Builder

	// Handle outlet filter
	if filter.OutletIDs != "" {
		where.WriteString(" AND htc.outlet_fkid IN @outletIds ")
	}

	var statusValues []string
	// Handle status filter
	if filter.Status != "" {
		statusMap := map[string]string{
			"pending":  "0",
			"approved": "1",
			"rejected": "3",
		}
		for _, s := range strings.Split(filter.Status, ",") {
			if val, exists := statusMap[strings.ToLower(s)]; exists {
				statusValues = append(statusValues, val)
			}
		}
		if len(statusValues) > 0 {
			where.WriteString(" AND htc.trc_status IN @statusValues ")
		}
	}

	// Handle date filter
	if filter.StartDate != "" {
		where.WriteString(" AND htc.trc_date_start >= STR_TO_DATE(@startDate, '%d-%m-%Y') ")
	}
	if filter.EndDate != "" {
		where.WriteString(" AND htc.trc_date_end <= STR_TO_DATE(@endDate, '%d-%m-%Y') ")
	}

	// Apply parameters to query
	query, queryParams := mysql.MapParam(query, map[string]any{
		"adminId":      adminID,
		"outletIds":    strings.Split(filter.OutletIDs, ","),
		"statusValues": statusValues,
		"startDate":    filter.StartDate,
		"endDate":      filter.EndDate,
		"WHERE":        where.String(),
	})

	var results []domain.CutiV2Detail
	err := m.Query(query, queryParams...).PrintSql().Model(&results)
	if log.IfError(err) {
		return nil, err
	}

	// Add row numbers
	for k, v := range results {
		if v.NO == 0 {
			v.NO = k + 1
		}
	}

	return results, nil
}

// CheckExistingTimeOff checks for any overlapping time off requests
func (m *mySQLCutiRepository) CheckExistingTimeOff(employeeID string, startDate, endDate string) ([]domain.TimeOffConflict, error) {
	query := `
		SELECT DISTINCT
			htc.trc_id,
			DATE_FORMAT(htc.trc_date_start, '%d-%m-%Y') as start_date,
			DATE_FORMAT(htc.trc_date_end, '%d-%m-%Y') as end_date,
			dtrc.dtrc_status as status,
			CASE 
				WHEN dtrc.dtrc_status = '0' THEN 'Pending'
				WHEN dtrc.dtrc_status = '1' THEN 'Approved'
				WHEN dtrc.dtrc_status = '2' THEN 'Partially Approved'
				WHEN dtrc.dtrc_status = '3' THEN 'Rejected'
				WHEN dtrc.dtrc_status = '4' THEN 'Canceled'
				ELSE 'Unknown'
			END as status_text,
			htc.trc_reason as reason
		FROM hrm_trans_cuti htc
		JOIN hrm_detail_trans_cuti dtrc ON dtrc.trc_id = htc.trc_id
		WHERE htc.hrm_employee_fkid = ?
		AND dtrc.dtrc_status IN ('0', '1')  -- Only check pending and approved dates
		AND (
			-- Check if any date in the new request falls within an existing request
			(dtrc.dtrc_date <= ? AND dtrc.dtrc_date >= ?)
			OR
			-- Check if any date in an existing request falls within the new request
			(dtrc.dtrc_date >= ? AND dtrc.dtrc_date <= ?)
		)
		ORDER BY start_date ASC`

	var conflicts []domain.TimeOffConflict
	err := m.Query(query,
		employeeID,
		endDate, startDate, // For first condition
		startDate, endDate, // For second condition
	).PrintSql().Model(&conflicts)

	log.IfError(err)
	return conflicts, err
}
