package usecase

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"gitlab.com/backend/api-hrm/core/util/fcmessage"
	"gitlab.com/backend/api-hrm/domain"
)

type cutiUseCase struct {
	cutiRepository domain.CutiRepository
}

// NewCutiUseCase func
func NewCutiUseCase(c domain.CutiRepository) domain.CutiUseCase {
	return &cutiUseCase{cutiRepository: c}
}

// Fetch usecase
func (c *cutiUseCase) Fetch(adminID int) ([]domain.CutiDetail, error) {
	return c.cutiRepository.Fetch(adminID)
}

// AddCuti
func (c *cutiUseCase) AddCuti(cuti map[string]interface{}) (int64, error) {
	cuti["trc_created"] = time.Now().In(time.FixedZone("GMT+7", 7*60*60)).Format("2006-01-02 15:04:05")
	cuti["trc_updated"] = time.Now().In(time.FixedZone("GMT+7", 7*60*60)).Format("2006-01-02 15:04:05")
	return c.cutiRepository.AddCuti(cuti)
}

// AddCutiV2
func (c *cutiUseCase) AddCutiV2(cuti domain.AddCutiRequest) (int64, error) {
	cuti.TrcDateStart = strings.TrimSpace(cuti.TrcDateStart)
	cuti.TrcDateEnd = strings.TrimSpace(cuti.TrcDateEnd)
	log.Info("addCuti: %v", cast.ToString(cuti))
	//input validation
	// loc, _ := time.LoadLocation("Asia/Jakarta")
	startDateParsed, _ := time.Parse("2006-01-02", (cuti.TrcDateStart))
	// if startDateParsed.Before(time.Now().In(loc)) {
	// 	return 0, fmt.Errorf("start date cannot be in the past")
	// }

	endDateParsed, _ := time.Parse("2006-01-02", (cuti.TrcDateEnd))
	if endDateParsed.Before(startDateParsed) {
		return 0, fmt.Errorf("end date cannot be before start date")
	}
	log.Info("cuti date: %v - %v", startDateParsed, endDateParsed)

	//validate remaining time off
	dayOffRemains, err := c.FetchDayOffRemains(cast.ToInt(cuti.HrmEmployeeID))
	if log.IfError(err) {
		return 0, err
	}

	dates := cast.DateRanges((cuti.TrcDateStart), (cuti.TrcDateEnd))
	if len(dates) > dayOffRemains.SisaCuti {
		return 0, domain.ExceptionWithCode{
			Message:   fmt.Sprintf("employee has only %d remaining leave days", dayOffRemains.SisaCuti),
			Code:      3,
			ErrorCode: "insufficient_leave_days",
		}
	}

	// Check for existing time off requests
	conflicts, err := c.cutiRepository.CheckExistingTimeOff(cuti.HrmEmployeeID, cuti.TrcDateStart, cuti.TrcDateEnd)
	if err != nil {
		return 0, fmt.Errorf("error checking existing time off: %v", err)
	}

	log.Info("conflic cuti: %v", cast.ToString(conflicts))
	if len(conflicts) > 0 {
		// Build detailed error message
		var msg strings.Builder
		msg.WriteString("Cannot request time off due to existing requests:\n")
		for _, conflict := range conflicts {
			msg.WriteString(fmt.Sprintf("- %s to %s (Status: %s)\n",
				conflict.StartDate,
				conflict.EndDate,
				conflict.StatusText))
		}
		return 0, fmt.Errorf(msg.String())
	}

	//add cuti
	err = c.cutiRepository.AddCutiV2(domain.AddCutiParam{
		AddCutiRequest: cuti,
	})
	log.IfError(err)
	return 0, err
}

// AddAttch
func (c *cutiUseCase) AddAttch(attachment []map[string]interface{}) error {
	return c.cutiRepository.AddAttch(attachment)
}

func (c *cutiUseCase) AddDetailAttch(dtAttach map[string]interface{}) (int64, error) {
	return c.cutiRepository.AddDetailAttch(dtAttach)
}

func (c *cutiUseCase) AddDetailTransCuti(details map[string]interface{}) (int64, error) {
	return c.cutiRepository.AddDetailTransCuti(details)
}

func (c *cutiUseCase) AddRecordCuti(record map[string]interface{}) error {
	return c.cutiRepository.AddRecordCuti(record)
}

func (c *cutiUseCase) FetchDayOffRemains(empID int) (domain.DayOffRemains, error) {
	return c.cutiRepository.FetchDayOffRemains(empID)
}

func (c *cutiUseCase) FetchDetailsCuti(trcID, adminID int) ([]domain.DetailsCuti, error) {
	return c.cutiRepository.FetchDetailsCuti(trcID, adminID)
}

func (c *cutiUseCase) FetchAttach(trcID int) ([]domain.Attachment, error) {
	return c.cutiRepository.FetchAttach(trcID)
}

func (c *cutiUseCase) FetchDetailsAttch(trcID int) ([]domain.DetailsAttach, error) {
	return c.cutiRepository.FetchDetailsAttch(trcID)
}

func (c *cutiUseCase) FetchRecord(trcID int) ([]domain.Record, error) {
	return c.cutiRepository.FetchRecord(trcID)
}

func (c *cutiUseCase) FetchEmployee() ([]domain.CutiEmployee, error) {
	return c.cutiRepository.FetchEmployee()
}

func (c *cutiUseCase) FetchAdmin() ([]domain.Admin, error) {
	return c.cutiRepository.FetchAdmin()
}

func (c *cutiUseCase) SaveDetailCuti(cuti domain.SaveDetailCutiRequest) error {
	log.Info("save cuti: %v", cast.ToString(cuti))
	err := c.cutiRepository.SaveDetailCuti(cuti)
	if log.IfError(err) {
		return err
	}

	dateMap := map[string]any{}
	reasonMap := map[string]any{}
	statusMap := map[string]any{}

	for i, v := range cuti.Idx {
		for _, w := range cuti.Status[i] {
			statusMap[v] = w.(string)
		}
	}

	for i, v := range cuti.Idx {
		for _, w := range cuti.Date[i] {
			if w == nil {
				dateMap[v] = "Document"
			} else if w == "null" {
				dateMap[v] = "Document"
			} else {
				dateMap[v] = w.(string)
			}
		}
	}

	for _, l := range cuti.Reasons {
		for k, v := range l {
			reasonMap[k] = v
		}
	}

	mess := map[string]any{}
	for _, m := range cuti.Idx {
		if reasonMap[m] != nil && statusMap[m].(string) == "2" {
			var stts string
			if statusMap[m].(string) == "1" {
				stts = "Approved"
			} else if statusMap[m].(string) == "2" {
				stts = "Rejected"
			} else {
				stts = "Cancel"
			}

			mess[m] = []string{
				dateMap[m].(string), stts, reasonMap[m].([]string)[0],
			}
		} else {
			var stts string
			if statusMap[m].(string) == "1" {
				stts = "Approved"
			} else if statusMap[m].(string) == "2" {
				stts = "Rejected"
			} else {
				stts = "Cancel"
			}
			mess[m] = []string{
				dateMap[m].(string), stts, "",
			}
		}
	}

	msg := "Tanggal | Status | Reason \n"
	for _, v := range mess {
		msg += strings.Join(v.([]string), " ") + "\n"
	}

	type User struct {
		UserID string
		TrcID  string
		Reason any
	}

	var s string
	var sArr []string

	for _, v := range cuti.Status {
		for _, r := range v {
			s += r.(string) + ","
			sArr = append(sArr, r.(string))
		}
	}

	s = s[:len(s)-1]

	sNotif := "Disetujui"

	if len(sArr) > 1 {
		if cast.ContainsStr(sArr, "2") {
			sNotif = "Ditolak"
		}
		if cast.ContainsStr(sArr, "3") {
			sNotif = "Dibatalkan"
		}
	}
	if len(sArr) == 1 {
		if sArr[0] == "1" {
			sNotif = "Disetujui"
		}

		if sArr[0] == "2" {
			sNotif = "Ditolak"
		}

		if sArr[0] == "3" {
			sNotif = "Dibatalkan"
		}
	}

	var tipe string
	for _, val := range cuti.StatusInfo {
		for _, v := range val {
			fmt.Printf("tipe %v \n", v)
			if v.(string) == "0" {
				tipe = "Cuti"
			}
			if v.(string) == "1" {
				tipe = "Izin"
			}

			if v.(string) == "2" {
				tipe = "Sakit"
			}
		}
	}

	title := fmt.Sprintf("Pengajuan %v %v", tipe, sNotif)

	u := &User{UserID: cuti.User, TrcID: cuti.TrcID, Reason: s}
	b, _ := json.Marshal(u)

	loc, _ := time.LoadLocation("Asia/Jakarta")
	timeNow := time.Now().In(loc).Unix()
	message := map[string]any{
		"title":             title,
		"message":           msg,
		"data_created":      timeNow,
		"type":              "hris",
		"receiver_type":     "employee",
		"receiver_id":       cuti.User,
		"admin_fkid":        cuti.Admin,
		"notification_type": "general",
		"notification_data": b,
	}
	messageId, err := c.SaveMessage(message)
	if err != nil {
		log.IfError(err)
		return err
	}

	// Add message ID to the response
	message["id"] = messageId

	notificationData := map[string]string{
		"message_id":        cast.ToString(messageId),
		"notification_type": "general",
	}

	id, err := strconv.Atoi(cuti.User)
	if err != nil {
		fmt.Printf("fetching employee id error: %v \n", err)
	}
	devToken, err := c.GetUserDeviceToken(id)
	if err != nil {
		fmt.Println(err)
		return err
	}
	var tokens []string
	for _, tk := range devToken {
		tokens = append(tokens, tk.Token)
	}

	log.Info("send notif to %v tokens", len(tokens))
	fcmessage.SendMessage(title, msg, notificationData, tokens...)
	return nil
}

func (c *cutiUseCase) CancelCuti(data map[string]interface{}) error {
	return c.cutiRepository.CancelCuti(data)
}

func (c *cutiUseCase) DeleteCuti(data map[string]interface{}) error {
	log.Info("delete cuti: %v", cast.ToString(data))
	err := c.cutiRepository.DeleteCuti(data)
	return err
}

func (c *cutiUseCase) FetchEmployeeOutlet(empID int) ([]domain.EmployeeOutletID, error) {
	return c.cutiRepository.FetchEmployeeOutlet(empID)
}

func (c *cutiUseCase) FetchShift(officeID int, shiftCode string) ([]domain.ShiftTime, error) {
	return c.cutiRepository.FetchShift(officeID, shiftCode)
}

func (c *cutiUseCase) FetchEmployeeShift(empID int, outletID int, date string) ([]domain.EmployeeShift, error) {
	return c.cutiRepository.FetchEmployeeShift(empID, outletID, date)
}

func (c *cutiUseCase) FetchEmployeeCutiDetail(empID int) ([]domain.EmployeeCutiDetail, error) {
	return c.cutiRepository.FetchEmployeeCutiDetail(empID)
}

func (c *cutiUseCase) FetchEmpRole(empID int) ([]domain.EmpRole, error) {
	return c.cutiRepository.FetchEmpRole(empID)
}

func (c *cutiUseCase) FetchAttendance(outletID int, date string, empID int) ([]domain.Attendance, error) {
	return c.cutiRepository.FetchAttendance(outletID, date, empID)
}

func (c *cutiUseCase) Absensi(data map[string]interface{}) error {
	return c.cutiRepository.Absensi(data)
}

func (c *cutiUseCase) FetchCutiDetails(trcID int) ([]domain.DetailsCutiM, error) {
	// Get base cuti details
	details, err := c.cutiRepository.FetchCutiDetails(trcID)
	if err != nil {
		return nil, fmt.Errorf("error fetching cuti details: %v", err)
	}

	// Get attachments
	attachments, err := c.cutiRepository.FetchFile(trcID)
	if err != nil {
		return nil, fmt.Errorf("error fetching attachments: %v", err)
	}

	// Get records
	records, err := c.cutiRepository.FetchRecord(trcID)
	if err != nil {
		return nil, fmt.Errorf("error fetching records: %v", err)
	}

	// Get employee and admin data for records
	emp, err := c.cutiRepository.FetchEmployee()
	if err != nil {
		return nil, fmt.Errorf("error fetching employees: %v", err)
	}

	adm, err := c.cutiRepository.FetchAdmin()
	if err != nil {
		return nil, fmt.Errorf("error fetching admins: %v", err)
	}

	// Process records to include names
	for i := range records {
		if strconv.Itoa(records[i].Admin) != "null" {
			for j := range adm {
				if strconv.Itoa(records[i].Admin) == adm[j].AdminID {
					records[i].Name = adm[j].Name
				}
			}
		}

		if strconv.Itoa(records[i].User) != "null" {
			for k := range emp {
				if strconv.Itoa(records[i].User) == emp[k].ID {
					records[i].Name = emp[k].Name
				}
			}
		}
	}

	// If we have details, add attachments and records to the first item
	if len(details) > 0 {
		details[0].Attachments = attachments
		details[0].Records = records
	}

	return details, nil
}

func (c *cutiUseCase) FetchOutletLatLing(outletID []string) ([]domain.OutletLatLing, error) {
	return c.cutiRepository.FetchOutletLatLing(outletID)
}

func (c *cutiUseCase) FetchEmployeeDetails(employeeID int) ([]domain.EmployeeDetails, error) {
	return c.cutiRepository.FetchEmployeeDetails(employeeID)
}

func (c *cutiUseCase) FetchHrmEmpID(employeeID int) (domain.HrmEmpID, error) {
	return c.cutiRepository.FetchHrmEmpID(employeeID)
}

func (c *cutiUseCase) GetUserDeviceToken(empID int) ([]domain.DeviceToken, error) {
	return c.cutiRepository.GetUserDeviceToken(empID)
}

func (c *cutiUseCase) SaveMessage(data map[string]interface{}) (int64, error) {
	return c.cutiRepository.SaveMessage(data)
}

func (c *cutiUseCase) FetchFile(trcID int) ([]domain.FileCuti, error) {
	return c.cutiRepository.FetchFile(trcID)
}

func (c *cutiUseCase) FetchEmpType(empID int) ([]domain.EmpType, error) {
	return c.cutiRepository.FetchEmpType(empID)
}

func (c *cutiUseCase) FetchSingleEmployeeCuti(trcID int) (domain.EmployeeCutiDetail, error) {
	return c.cutiRepository.FetchSingleEmployeeCuti(trcID)
}

// FetchCutiV2 usecase
func (c *cutiUseCase) FetchCutiV2(adminID int, filter domain.CutiFilter) ([]domain.CutiV2Detail, error) {
	result, err := c.cutiRepository.FetchCutiV2(adminID, filter)
	if result == nil || len(result) == 0 {
		result = make([]domain.CutiV2Detail, 0)
	}
	return result, err
}

// FetchCompleteDetails gets all details related to a cuti request
func (c *cutiUseCase) FetchCompleteDetails(cutiID, businessID int) (map[string]interface{}, error) {
	// Get basic cuti details
	res, err := c.FetchDetailsCuti(cutiID, businessID)
	if err != nil {
		return nil, fmt.Errorf("error fetch details cuti: %v", err)
	}

	// Get attachments
	attach, err := c.FetchAttach(cutiID)
	if err != nil {
		return nil, fmt.Errorf("error fetching attachment: %v", err)
	}

	// Get attachment details
	da, err := c.FetchDetailsAttch(cutiID)
	if err != nil {
		return nil, fmt.Errorf("error fetching details attach: %v", err)
	}

	// Get employees
	emp, err := c.FetchEmployee()
	if err != nil {
		return nil, fmt.Errorf("error fetching employee: %v", err)
	}

	// Get admins
	adm, err := c.FetchAdmin()
	if err != nil {
		return nil, fmt.Errorf("error fetching detail admin: %v", err)
	}

	// Get records
	record, err := c.FetchRecord(cutiID)
	if err != nil {
		return nil, fmt.Errorf("error fetching record: %v", err)
	}

	// Process records to include names
	for i := range record {
		if strconv.Itoa(record[i].Admin) != "null" {
			for j := range adm {
				if strconv.Itoa(record[i].Admin) == adm[j].AdminID {
					record[i].Name = adm[j].Name
				}
			}
		}

		if strconv.Itoa(record[i].Admin) != "null" {
			for k := range emp {
				if strconv.Itoa(record[i].User) == emp[k].ID {
					record[i].Name = emp[k].Name
				}
			}
		}
	}

	// Convert cuti details to map
	var allResult []any
	for _, l := range res {
		result := cast.StructToMap(l)
		allResult = append(allResult, result)
	}
	allResult = append(allResult, attach, da, record)

	return map[string]interface{}{
		"data": allResult,
	}, nil
}
