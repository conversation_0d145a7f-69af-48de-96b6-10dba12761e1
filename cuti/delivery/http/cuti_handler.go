package http

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/bucket"
	"gitlab.com/backend/api-hrm/core/util/cast"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/backend/api-hrm/domain"
)

// CutiHandler struct
type CutiHandler struct {
	CutiUseCase domain.CutiUseCase
}

// NewCutiHandler func
func NewCutiHandler(app *fiber.App, uc domain.CutiUseCase) {
	handler := &CutiHandler{CutiUseCase: uc}
	// web API
	v1 := app.Group("/v1")
	v1.Get("/cuti", handler.Fetch)
	v1.Get("/details_cuti/:id", handler.FetchDetailsCuti)
	v1.Post("/add_cuti", handler.AddCuti)
	v1.Post("/save_details_cuti", handler.SaveDetailCuti)
	v1.Post("/cancel_cuti", handler.CancelCuti)
	v1.Post("/delete_cuti", handler.DeleteCuti)
	// mobile API
	v2 := app.Group("/v2")
	v2.Get("/shift_time/:outlet_id/:emp_id", handler.FetchEmployeeOutlet)
	v2.Get("/cuti_time_details/:emp_id", handler.FetchEmployeeCutiDetail)
	v2.Get("/emp_role/:emp_id", handler.FetchEmpRole)
	v2.Get("/attendance/:outlet_id/:date/:emp_id", handler.FetchAttendance)
	v2.Get("/fetch_cuti_details/:trc_id", handler.FetchCutiDetails)
	v2.Get("/fetch_single_cuti_details/:trc_id", handler.FetchSingleEmployeeCuti)
	v2.Get("/fetch_outlet_latlng/:outlet_id", handler.FetchOutletLatLing)
	v2.Get("/fetch_employee_details/:emp_id", handler.FetchEmployeeDetails)
	v2.Get("/fetch_employee_type/:emp_id", handler.FetchEmpType)
	v2.Get("/cuti", handler.FetchCutiV2)
	v2.Post("/add_cuti", handler.AddCutiMobile)
	v2.Post("/scan_attendance", handler.Absensi)
}

// Fetch get all cuti
// @Summary Get all cuti records based on the business ID from the header.
// @Description Get all cuti records based on the business ID from the header.
// @Tags cuti
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Success 200 {array} domain.Cuti
// @Failure 500 {object} object "Internal server error"
// @Router /v1/cuti [get]
func (ct *CutiHandler) Fetch(c *fiber.Ctx) error {
	businessId := c.Get("business_id")
	// adminID := c.Get("user_id")
	businessID, _ := strconv.Atoi(businessId)

	cuti, err := ct.CutiUseCase.Fetch(businessID)
	if err != nil {
		fmt.Println("error: ", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "fetch cuti error",
			"error":   err,
		})
	}
	return c.JSON(cuti)
}

// AddCutiMobile handle func
// @Summary Add a new cuti record (Mobile)
// @Description Add a new cuti record with details and optional document attachments from mobile app
// @Tags cuti
// @Accept multipart/form-data
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param hrm_employee_id formData string true "Employee ID"
// @Param outlet_fkid formData string true "Outlet ID(s) (comma separated)"
// @Param trc_created formData string true "Creation timestamp"
// @Param trc_date_start formData string true "Start date of cuti (YYYY-MM-DD)"
// @Param trc_date_end formData string true "End date of cuti (YYYY-MM-DD)"
// @Param trc_reason formData string true "Reason for cuti"
// @Param trc_status formData string true "Status of cuti"
// @Param type formData string true "Type of cuti"
// @Param document formData file false "Document attachments"
// @Param trc_id formData string false "Transaction Cuti ID (for updates)"
// @Success 200 {object} fiber.Map "Success response with message and status"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v2/add_cuti [post]
func (ct *CutiHandler) AddCutiMobile(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		fmt.Printf("wrong form key: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "wrong form key",
			"error":   err,
		})
	}
	loc, _ := time.LoadLocation("Asia/Jakarta")

	var request domain.CutiMobileRequest
	if err := c.BodyParser(&request); err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error parsing request body",
			"error":   err,
		})
	}

	files := form.File["document"]
	log.Info("addCuti Mobile with %v files: %v", len(files), cast.ToString(request))

	if request.TrcID != "" && request.TrcID != "null" {
		var attach []string
		if len(files) != 0 {
			for _, file := range files {
				f, err := file.Open()
				if err != nil {
					return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
				}
				url, err := bucket.UploadBucket(f, file.Filename, request.HrmEmployeeID, true)
				if err != nil {
					return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
				}
				attach = append(attach, url)
			}
		}

		// mapping attachment file
		var attachment []map[string]any
		if len(attach) != 0 {
			for _, v := range attach {
				attch := map[string]any{
					"trc_id":          request.TrcID,
					"hrm_employee_id": request.HrmEmployeeID,
					"file":            v,
				}
				attachment = append(attachment, attch)
			}
		}
		if len(attachment) != 0 {
			err := ct.CutiUseCase.AddAttch(attachment)
			if err != nil {
				fmt.Printf("Add attach error: %v", err)
				return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
					"message": "Update gagal",
					"error":   err,
					"status":  0,
				})
			}
		}
		return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
			"message": "Cuti berhasil di ubah",
			"error":   err,
			"status":  1,
		})
	} else {
		dates := cast.DateRanges(request.DateStart, request.DateEnd)
		arrOutlet := strings.Split(request.OutletFkid, ",")

		var attach []string
		if len(files) != 0 {
			for _, file := range files {
				f, err := file.Open()
				if err != nil {
					return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
				}
				url, err := bucket.UploadBucket(f, file.Filename, request.HrmEmployeeID, true)
				if err != nil {
					return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
				}
				attach = append(attach, url)
			}
		}

		tp, _ := strconv.Atoi(request.Type)
		// mapping input params
		inputParams := map[string]any{
			"hrm_employee_id": request.HrmEmployeeID,
			"outlet_fkid":     arrOutlet[0],
			"trc_date_start":  request.DateStart,
			"trc_date_end":    request.DateEnd,
			"trc_reason":      request.TrcReason,
			"trc_created":     request.TrcCreated,
			"trc_status":      request.TrcStatus,
			"type":            tp,
		}

		// Insert employe data to hrm_trans_cuti
		id, err := ct.CutiUseCase.AddCuti(inputParams)
		if err != nil {
			fmt.Printf("Add cuti error: %v", err)
			return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
				"message": "Add cuti failed",
				"error":   err,
			})
		}
		var attachment []map[string]any

		// mapping attachment file
		if len(attach) != 0 {
			for _, v := range attach {
				attch := map[string]any{
					"trc_id":          id,
					"hrm_employee_id": request.HrmEmployeeID,
					"file":            v,
				}
				attachment = append(attachment, attch)
			}
		}

		// mapping details day off
		var detailsDayOff map[string]any

		// mapping detail record cuti
		var detailsRecord map[string]any
		var ownerType any
		var userType any

		typesEmp := c.Get("user_type")
		userID := c.Get("user_id")

		if typesEmp == "employee" {
			ownerType = nil
			userType = userID
		} else {
			ownerType = userID
			userType = nil
		}

		log.Info("attachment size: %v", len(attachment))
		// add cuti attachment
		if len(attachment) != 0 {
			err := ct.CutiUseCase.AddAttch(attachment)
			if err != nil {
				fmt.Printf("Add attach error: %v", err)
				return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
					"message": "Add attach failed",
					"error":   err,
				})
			}
		}

		for _, d := range dates {
			detailsDayOff = map[string]any{
				"trc_id":            id,
				"outlet_fkid":       arrOutlet[0],
				"employee_fkid":     request.HrmEmployeeID,
				"dtrc_date":         d,
				"dtrc_status":       "0",
				"type":              tp,
				"dtrc_date_updated": time.Now().In(loc).Format("2006-01-02 15:04:05"),
			}
			lastID, err := ct.CutiUseCase.AddDetailTransCuti(detailsDayOff)
			if err != nil {
				fmt.Printf("Add details trans cuti error: %v", err)
				return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
					"message": "Add detail trans cuti failed",
					"error":   err,
				})
			}
			detailsRecord = map[string]any{
				"dtrc_fkid":     id,
				"dtrc_id":       lastID,
				"user":          userType,
				"admin":         ownerType,
				"email":         "",
				"date_cuti":     d,
				"from_status":   "0",
				"to_status":     nil,
				"reason_status": nil,
				"date_updated":  time.Now().In(loc).Format("2006-01-02 15:04:05"),
			}
			err = ct.CutiUseCase.AddRecordCuti(detailsRecord)
			if err != nil {
				fmt.Printf("add details trans cuti error: %v", err)
				return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
					"message": "Add details trans cuti failed",
					"error":   err,
				})
			}
			detailsDayOff = map[string]any{}
			detailsRecord = map[string]any{}
		}

		detailAttach := map[string]any{
			"trc_fkid":    id,
			"status":      "0",
			"date_update": time.Now().In(loc).Format("2006-01-02 15:04:05"),
		}
		daid, err := ct.CutiUseCase.AddDetailAttch(detailAttach)

		if err != nil {
			fmt.Printf("add detail attach error: %v", err)
			return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
				"message": "Add detail attach failed",
				"error":   err,
			})
		}

		record := map[string]any{
			"dtrc_fkid":     id,
			"dtrc_id":       daid,
			"user":          userType,
			"admin":         ownerType,
			"email":         "",
			"date_cuti":     nil,
			"from_status":   "0",
			"to_status":     nil,
			"reason_status": nil,
			"date_updated":  time.Now().In(loc).Format("2006-01-02 15:04:05"),
		}
		err = ct.CutiUseCase.AddRecordCuti(record)
		if err != nil {
			fmt.Printf("Add cuti error: %v", err)
			return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
				"message": "Cuti gagal di tambahan",
				"error":   err,
				"status":  0,
			})
		}

		return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
			"message": "Cuti berhasil di tambahkan",
			"status":  1,
		})
	}

	return nil
}

// AddCuti handle func
// @Summary Add a new cuti record (Web)
// @Description Add a new cuti record with details and optional document attachments.
// @Tags cuti
// @Accept multipart/form-data
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param inputOutletId formData string true "Outlet ID"
// @Param inputEmployeeId formData string true "Employee ID"
// @Param typeInput formData string true "Type of cuti"
// @Param inputStartDate formData string true "Start date of cuti (YYYY-MM-DD)"
// @Param inputEndDate formData string true "End date of cuti (YYYY-MM-DD)"
// @Param inputDesc formData string true "Description/Reason for cuti"
// @Param inputDocument[] formData file false "Document attachments"
// @Success 200 {object} object "Success response"
// @Failure 500 {object} object "Internal server error"
// @Failure 400 {object} object "Bad request or validation error"
// @Router /v1/add_cuti [post]
// @Summary Add a new cuti request
// @Description Create a new cuti request with details including dates, reason, and attachments
// @Tags cuti
// @Accept multipart/form-data
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param user_type formData string true "User type (employee/admin)"
// @Param user_id formData string true "User ID"
// @Param inputOutletId formData string true "Outlet ID"
// @Param inputEmployeeId formData string true "Employee ID"
// @Param typeInput formData string true "Type of cuti (e.g., leave, sick, etc.)"
// @Param inputStartDate formData string true "Start date of cuti (YYYY-MM-DD)"
// @Param inputEndDate formData string true "End date of cuti (YYYY-MM-DD)"
// @Param inputDesc formData string true "Description of cuti"
// @Param inputDocument[] formData file false "Document attachments"
// @Success 200 {object} fiber.Map "Success response with message and status"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Failure 400 {object} fiber.Map "Bad request or validation error"
// @Router /v1/add_cuti [post]
func (ct *CutiHandler) AddCuti(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		fmt.Printf("Wrong form key: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "wrong form key",
			"error":   err,
		})
	}
	loc, err := time.LoadLocation("Asia/Jakarta")
	if log.IfError(err) {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error loading location",
			"error":   err,
		})
	}

	type cutiInput struct {
		UserType        string   `json:"user_type" form:"user_type"`
		UserID          string   `json:"user_id" form:"user_id"`
		InputOutletId   string   `json:"inputOutletId" form:"inputOutletId"`
		InputEmployeeId string   `json:"inputEmployeeId" form:"inputEmployeeId"`
		TypeInput       string   `json:"typeInput" form:"typeInput"`
		InputStartDate  string   `json:"inputStartDate" form:"inputStartDate"`
		InputEndDate    string   `json:"inputEndDate" form:"inputEndDate"`
		InputDesc       string   `json:"inputDesc" form:"inputDesc"`
		InputDocument   []string `json:"inputDocument" form:"inputDocument"`
	}

	userSession := domain.GetUserSessionFiber(c)
	adminID := userSession.UserId

	var input cutiInput
	err = c.BodyParser(&input)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error parsing body",
			"error":   err,
		})
	}

	fmt.Println("input add cuti ---> ", cast.ToString(input))

	input.InputStartDate = strings.TrimSpace(input.InputStartDate)
	input.InputEndDate = strings.TrimSpace(input.InputEndDate)
	files := form.File["inputDocument[]"]
	// dates := cast.DateRanges(cast.DateReverse(input.InputStartDate), cast.DateReverse(input.InputEndDate))

	// upload files to bucket and get link upload
	var attach []string
	if len(files) != 0 {
		for _, file := range files {
			f, err := file.Open()
			if err != nil {
				return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
			}
			url, err := bucket.UploadBucket(f, file.Filename, input.InputEmployeeId, true)
			if err != nil {
				return c.Status(fiber.ErrInternalServerError.Code).SendString(err.Error())
			}
			log.Info("uploaded file: %v", url)
			attach = append(attach, url)
		}
	}

	// mapping input params with date validation
	inputParams := domain.AddCutiRequest{
		HrmEmployeeID: input.InputEmployeeId,
		OutletFkid:    input.InputOutletId,
		TrcDateStart:  cast.DateReverse(input.InputStartDate),
		TrcDateEnd:    cast.DateReverse(input.InputEndDate),
		TrcReason:     input.InputDesc,
		TrcCreated:    time.Now().In(loc).Format("2006-01-02 15:04:05"),
		TrcStatus:     "0",
		Type:          input.TypeInput,
		Attachments:   attach,
		AdminFkid:     cast.ToInt(adminID),
		UserType:      input.UserType,
	}

	// checking remaining day off with improved error message
	// if res.SisaCuti <= 0 {
	// 	return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
	// 		"message": "Employee has no remaining leave days",
	// 		"error":   "no_remaining_days",
	// 		"status":  3,
	// 	})
	// } else if len(dates) > res.SisaCuti {
	// 	return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
	// 		"message": fmt.Sprintf("Employee has only %d remaining leave days", res.SisaCuti),
	// 		"error":   "insufficient_leave_days",
	// 		"status":  3,
	// 	})
	// }

	// Insert employe data to hrm_trans_cuti
	_, err = ct.CutiUseCase.AddCutiV2(inputParams)
	if err != nil {
		log.Info("error add cuti: %v", err)
		if errWithCode, ok := err.(domain.ExceptionWithCode); ok {
			return c.Status(errWithCode.Code).JSON(&fiber.Map{
				"message": errWithCode.Message,
				"status":  errWithCode.Code,
				"error":   errWithCode.ErrorCode,
			})
		}
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error add cuti",
			"error":   err.Error(),
		})
	}

	// // mapping details day off
	// var detailsDayOff map[string]any

	// // mapping detail record cuti
	// var detailsRecord map[string]any
	// var ownerType any
	// var userType any
	// if input.UserType == "employee" {
	// 	ownerType = nil
	// 	userType = input.UserID
	// } else if input.UserType == "admin" {
	// 	ownerType = adminID
	// 	userType = nil
	// }

	// for _, d := range dates {
	// 	detailsDayOff = map[string]any{
	// 		"trc_id":            id,
	// 		"outlet_fkid":       input.InputOutletId,
	// 		"employee_fkid":     input.InputEmployeeId,
	// 		"dtrc_date":         d,
	// 		"dtrc_status":       "0",
	// 		"type":              input.TypeInput,
	// 		"dtrc_date_updated": time.Now().In(loc).Format("2006-01-02 15:04:05"),
	// 	}
	// 	lastID, err := ct.CutiUseCase.AddDetailTransCuti(detailsDayOff)
	// 	if err != nil {
	// 		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
	// 			"message": "error add details trans cuti",
	// 			"error":   err,
	// 		})
	// 	}
	// 	detailsRecord = map[string]any{
	// 		"dtrc_fkid":     id,
	// 		"dtrc_id":       lastID,
	// 		"user":          userType,
	// 		"admin":         ownerType,
	// 		"email":         "",
	// 		"date_cuti":     d,
	// 		"from_status":   "0",
	// 		"to_status":     nil,
	// 		"reason_status": nil,
	// 		"date_updated":  time.Now().In(loc).Format("2006-01-02 15:04:05"),
	// 	}

	// 	for k, v := range detailsRecord {
	// 		fmt.Printf("%v : %v \n", k, v)
	// 	}

	// 	err = ct.CutiUseCase.AddRecordCuti(detailsRecord)
	// 	if err != nil {
	// 		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
	// 			"message": "add record cuti error",
	// 			"error":   err,
	// 		})
	// 	}
	// 	detailsDayOff = map[string]any{}
	// 	detailsRecord = map[string]any{}
	// }

	// detailAttach := map[string]any{
	// 	"trc_fkid":    id,
	// 	"status":      "0",
	// 	"date_update": time.Now().In(loc).Format("2006-01-02 15:04:05"),
	// }
	// daid, err := ct.CutiUseCase.AddDetailAttch(detailAttach)
	// if err != nil {
	// 	return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
	// 		"message": "add details attact error",
	// 		"error":   err,
	// 	})
	// }

	// record := map[string]any{
	// 	"dtrc_fkid":     id,
	// 	"dtrc_id":       daid,
	// 	"user":          userType,
	// 	"admin":         ownerType,
	// 	"email":         "",
	// 	"date_cuti":     nil,
	// 	"from_status":   "0",
	// 	"to_status":     nil,
	// 	"reason_status": nil,
	// 	"date_updated":  time.Now().In(loc).Format("2006-01-02 15:04:05"),
	// }
	// err = ct.CutiUseCase.AddRecordCuti(record)
	// if err != nil {
	// 	return c.Status(500).JSON(&fiber.Map{
	// 		"message": "Cuti gagal di tambahkan",
	// 		"status":  0,
	// 		"error":   err,
	// 	})
	// }

	return c.Status(200).JSON(&fiber.Map{
		"message": "Cuti berhasil di tambahkan",
		"status":  1,
	})
}

// FetchDetailsCuti handle func
// @Summary Get cuti details by ID
// @Description Get detailed information about a specific cuti record, including attachments and records.
// @Tags cuti
// @Accept json
// @Produce json
// @Param id path int true "Cuti ID"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} object "Cuti details, attachments, and records"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v1/details_cuti/{id} [get]
func (ct *CutiHandler) FetchDetailsCuti(c *fiber.Ctx) error {
	businessId := c.Get("business_id")
	businessID, _ := strconv.Atoi(businessId)
	cuti_id := c.Params("id")
	cutiID, _ := strconv.Atoi(cuti_id)

	result, err := ct.CutiUseCase.FetchCompleteDetails(cutiID, businessID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"message": "error fetching cuti details",
			"error":   err.Error(),
		})
	}

	return c.JSON(result["data"])
}

// SaveDetailCuti handle func
// @Summary Save/Update cuti details (Web)
// @Description Save or update details of a cuti record, including status, reason, and attachments.
// @Tags cuti
// @Accept multipart/form-data
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param inputTypeAdm formData string true "Input Type Admin"
// @Param inputUserID formData string true "Input User ID"
// @Param inputAdmin formData string true "Input Admin ID"
// @Param inputEmailAdm formData string true "Input Admin Email"
// @Param nik formData string true "NIK"
// @Param inputTransCutiId formData string true "Transaction Cuti ID"
// @Param emp_id formData string true "Employee ID"
// @Param outlet_id formData string true "Outlet ID"
// @Param emp_name formData string true "Employee Name"
// @Param email formData string true "Employee Email"
// @Param addDocument[] formData file false "Additional document attachments"
// @Success 200 {object} fiber.Map "Success response"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v1/save_details_cuti [post]
func (ct *CutiHandler) SaveDetailCuti(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "Wrong form key",
			"error":   err,
		})
	}

	// Parse form data into struct
	var formData domain.SaveDetailCutiRequest
	if err := c.BodyParser(&formData); err != nil {
		return c.Status(fiber.ErrBadRequest.Code).JSON(&fiber.Map{
			"message": "Failed to parse form data",
			"error":   err,
		})
	}

	var attach []string
	files := form.File["addDocument[]"]
	if len(files) != 0 {
		for _, file := range files {
			f, err := file.Open()
			if err != nil {
				return err
			}
			url, err := bucket.UploadBucket(f, file.Filename, formData.User, true)
			if err != nil {
				return err
			}
			attach = append(attach, url)
		}
	}
	var status []map[string]any
	var _status []map[string]any
	var reason []map[string]any
	var date []map[string]any
	var key []map[string]any
	var idx []string
	var i int
	for k, v := range form.Value {
		if k != "nik" && k != "emp_name" && k != "emp_id" && k != "email" {
			// get status
			if k[0:6] == "status" {
				s := map[string]any{
					k[7 : len(k)-1]: v[0],
				}
				status = append(status, s)
				s = map[string]any{}
			}

			// get _status
			if k[0:7] == "_status" {
				s := map[string]any{
					k[8 : len(k)-1]: v[0],
				}
				_status = append(_status, s)
				s = map[string]any{}
			}
			// get _date
			if k[0:5] == "_date" {
				if k[0:6] != "_date_" {
					d := map[string]any{
						k[6 : len(k)-1]: v[0],
					}
					date = append(date, d)
					ky := map[string]any{
						strconv.Itoa(i + 1): k[6 : len(k)-1],
					}
					key = append(key, ky)
					idx = append(idx, k[6:len(k)-1])
					ky = map[string]any{}
					d = map[string]any{}
				}
			}
			// get reason
			if k[0:6] == "reason" {
				r := map[string]any{
					k[7 : len(k)-1]: v,
				}
				reason = append(reason, r)
				r = map[string]any{}
			}
		}
	}

	// Update formData with processed data
	formData.Status = status
	formData.Date = date
	formData.Documents = attach
	formData.Keys = key
	formData.Reasons = reason
	formData.StatusInfo = _status
	formData.Idx = idx

	// SaveDetailCuti
	err = ct.CutiUseCase.SaveDetailCuti(formData)
	if err != nil {
		fmt.Printf("error updating cuti: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"statu":   0,
			"message": err,
		})
	}

	// dateMap := map[string]any{}
	// reasonMap := map[string]any{}
	// statusMap := map[string]any{}

	// for i, v := range idx {
	// 	for _, w := range status[i] {
	// 		statusMap[v] = w.(string)
	// 	}
	// }

	// for i, v := range idx {
	// 	for _, w := range date[i] {
	// 		if w == nil {
	// 			dateMap[v] = "Document"
	// 		} else if w == "null" {
	// 			dateMap[v] = "Document"
	// 		} else {
	// 			dateMap[v] = w.(string)
	// 		}
	// 	}
	// }

	// for _, l := range reason {
	// 	for k, v := range l {
	// 		reasonMap[k] = v
	// 	}
	// }

	// mess := map[string]any{}
	// for _, m := range idx {
	// 	if reasonMap[m] != nil && statusMap[m].(string) == "2" {
	// 		var stts string
	// 		if statusMap[m].(string) == "1" {
	// 			stts = "Approved"
	// 		} else if statusMap[m].(string) == "2" {
	// 			stts = "Rejected"
	// 		} else {
	// 			stts = "Cancel"
	// 		}

	// 		mess[m] = []string{
	// 			dateMap[m].(string), stts, reasonMap[m].([]string)[0],
	// 		}
	// 	} else {
	// 		var stts string
	// 		if statusMap[m].(string) == "1" {
	// 			stts = "Approved"
	// 		} else if statusMap[m].(string) == "2" {
	// 			stts = "Rejected"
	// 		} else {
	// 			stts = "Cancel"
	// 		}
	// 		mess[m] = []string{
	// 			dateMap[m].(string), stts, "",
	// 		}
	// 	}
	// }

	// msg := "Tanggal | Status | Reason \n"
	// for _, v := range mess {
	// 	msg += strings.Join(v.([]string), " ") + "\n"
	// }

	// type User struct {
	// 	UserID string
	// 	TrcID  string
	// 	Reason any
	// }

	// var s string
	// var sArr []string

	// for _, v := range status {
	// 	for _, r := range v {
	// 		s += r.(string) + ","
	// 		sArr = append(sArr, r.(string))
	// 	}
	// }

	// s = s[:len(s)-1]

	// sNotif := "Disetujui"

	// if len(sArr) > 1 {
	// 	if cast.ContainsStr(sArr, "2") {
	// 		sNotif = "Ditolak"
	// 	}
	// 	if cast.ContainsStr(sArr, "3") {
	// 		sNotif = "Dibatalkan"
	// 	}
	// }
	// if len(sArr) == 1 {
	// 	if sArr[0] == "1" {
	// 		sNotif = "Disetujui"
	// 	}

	// 	if sArr[0] == "2" {
	// 		sNotif = "Ditolak"
	// 	}

	// 	if sArr[0] == "3" {
	// 		sNotif = "Dibatalkan"
	// 	}
	// }

	// var tipe string
	// for _, val := range _status {
	// 	for _, v := range val {
	// 		fmt.Printf("tipe %v \n", v)
	// 		if v.(string) == "0" {
	// 			tipe = "Cuti"
	// 		}
	// 		if v.(string) == "1" {
	// 			tipe = "Izin"
	// 		}

	// 		if v.(string) == "2" {
	// 			tipe = "Sakit"
	// 		}
	// 	}
	// }

	// title := fmt.Sprintf("Pengajuan %v %v", tipe, sNotif)

	// u := &User{UserID: formData.User, TrcID: formData.TrcID, Reason: s}
	// b, _ := json.Marshal(u)

	// loc, _ := time.LoadLocation("Asia/Jakarta")
	// timeNow := time.Now().In(loc).Unix()
	// message := map[string]any{
	// 	"title":             title,
	// 	"message":           msg,
	// 	"data_created":      timeNow,
	// 	"type":              "hris",
	// 	"receiver_type":     "employee",
	// 	"receiver_id":       formData.User,
	// 	"admin_fkid":        formData.Admin,
	// 	"notification_type": "general",
	// 	"notification_data": b,
	// }
	// messageID, err := ct.CutiUseCase.SaveMessage(message)
	// if err != nil {
	// 	fmt.Printf("error saving message: %v", err)
	// 	return c.Status(500).JSON(&fiber.Map{
	// 		"status":  0,
	// 		"message": err,
	// 	})
	// }

	// // Add message ID to the response
	// message["id"] = messageID

	// notificationData := map[string]string{
	// 	"message_id":        cast.ToString(messageID),
	// 	"notification_type": "general",
	// }

	// id, err := strconv.Atoi(formData.User)
	// if err != nil {
	// 	fmt.Printf("fetching employee id error: %v \n", err)
	// }
	// devToken, err := ct.CutiUseCase.GetUserDeviceToken(id)
	// if err != nil {
	// 	fmt.Println(err)
	// 	return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
	// 		"message": "Get user device failed",
	// 		"error":   err,
	// 	})
	// }
	// var tokens []string
	// for _, tk := range devToken {
	// 	tokens = append(tokens, tk.Token)
	// }

	// log.Info("send notif to %v tokens", len(tokens))
	// fcmessage.SendMessage(title, msg, notificationData, tokens...)

	return c.Status(200).JSON(&fiber.Map{
		"message": "Data detail cuti berhasil di update",
		"status":  1,
		"data": map[string]any{
			"email":    formData.Email,
			"emp_name": formData.EmpName,
		},
	})
}

// CancelCuti handle func
// @Summary Cancel a cuti record (Web)
// @Description Cancel an existing cuti record.
// @Tags cuti
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param request body object true "Cuti cancellation details"
// @Param request.trc_id body int true "Transaction Cuti ID"
// @Param request.emp_id body int true "Employee ID"
// @Param request.outlet_id body int true "Outlet ID"
// @Param request.date_start body string true "Start date of cuti (YYYY-MM-DD)"
// @Param request.date_end body string true "End date of cuti (YYYY-MM-DD)"
// @Success 200 {object} fiber.Map "Success response"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v1/cancel_cuti [post]
func (ct *CutiHandler) CancelCuti(c *fiber.Ctx) error {
	type req struct {
		TrcID     int    `json:"trc_id"`
		EmpID     int    `json:"emp_id"`
		OutletID  int    `json:"outlet_id"`
		DateStart string `json:"date_start"`
		DateEnd   string `json:"date_end"`
	}

	var body req
	c.BodyParser(&body)
	data := cast.StructToMap(body)
	err := ct.CutiUseCase.CancelCuti(data)
	if err != nil {
		return c.Status(500).JSON(&fiber.Map{
			"message": "Gagal cancel cuti",
			"status":  0,
			"error":   err,
		})
	}

	return c.Status(200).JSON(&fiber.Map{
		"message": "Cuti berhasil di cancel",
		"status":  1,
	})
}

// DeleteCuti handle func
// @Summary Delete a cuti record (Web)
// @Description Delete an existing cuti record and associated files.
// @Tags cuti
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param request body object true "Cuti deletion details"
// @Param request.trc_id body int true "Transaction Cuti ID"
// @Param request.outlet_id body int true "Outlet ID"
// @Param request.emp_id body int true "Employee ID"
// @Param request.start_date body string true "Start date of cuti (YYYY-MM-DD)"
// @Param request.end_date body string true "End date of cuti (YYYY-MM-DD)"
// @Success 200 {object} fiber.Map "Success response"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v1/delete_cuti [post]
func (ct *CutiHandler) DeleteCuti(c *fiber.Ctx) error {
	type req struct {
		TrcID     int    `json:"trc_id"`
		OutletID  int    `json:"outlet_id"`
		EmpID     int    `json:"emp_id"`
		StartDate string `json:"start_date"`
		EndDate   string `json:"end_date"`
	}
	var body req
	c.BodyParser(&body)
	files, err := ct.CutiUseCase.FetchFile(body.TrcID)
	if err != nil {
		fmt.Println(err)
	}
	pathFile := []string{}
	if len(files) != 0 {
		for _, v := range files {
			pathFile = append(pathFile, v.File[55:len(v.File)])
		}
	}
	if len(pathFile) != 0 {
		for _, v := range pathFile {
			err := bucket.DeleteBucket("uniq-187911.appspot.com", v, true)
			if err != nil {
				fmt.Println(err)
			}
		}
	}

	data := cast.StructToMap(body)
	err = ct.CutiUseCase.DeleteCuti(data)
	if err != nil {
		return c.Status(500).JSON(&fiber.Map{
			"message": "Gagal menghapus cuti",
			"status":  0,
			"error":   err,
		})
	}
	return c.Status(200).JSON(&fiber.Map{
		"message": "Cuti berhasil di hapus",
		"status":  1,
	})
}

// FetchEmployeeOutlet handler func
// @Summary Get employee shift time by outlet and employee ID (Mobile)
// @Description Get the shift time for a specific employee at a given outlet for the current date.
// @Tags cuti
// @Accept json
// @Produce json
// @Param outlet_id path int true "Outlet ID"
// @Param emp_id path int true "Employee ID"
// @Success 200 {array} domain.ShiftTime "Shift time data"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v2/shift_time/{outlet_id}/{emp_id} [get]
func (ct *CutiHandler) FetchEmployeeOutlet(c *fiber.Ctx) error {
	emp_id := c.Params("emp_id")
	empID, _ := strconv.Atoi(emp_id)
	outlet_id := c.Params("outlet_id")
	outletID, _ := strconv.Atoi(outlet_id)
	date := time.Now()
	dateString := date.Format("2006-01-02")
	// get outlet id
	// results, err := ct.CutiUseCase.FetchEmployeeOutlet(empID)
	// if err != nil {
	// 	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
	// 		"message": "error fetching employee outlet",
	// 		"error":   err,
	// 		"status":  c.Response().StatusCode(),
	// 	})
	// }

	// fmt.Printf("employee outlets: %v", results)
	// outletID := make([]map[string]any, 0)
	// for _, v := range results {
	// 	ID := cast.StructToMap(v)
	// 	outletID = append(outletID, ID)
	// }
	// get employee shift
	empShift, err := ct.CutiUseCase.FetchEmployeeShift(empID, outletID, dateString)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching employee shift",
			"error":   err,
		})
	}

	// fmt.Printf("employee shift: %v", empShift[0].ShiftID)
	// get shift
	if len(empShift) != 0 {
		shiftTime, err := ct.CutiUseCase.FetchShift(outletID, empShift[0].ShiftID)
		if err != nil {
			return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
				"message": "error fetching shifts",
				"error":   err,
			})
		}
		return c.JSON(shiftTime)
	}
	shift := []domain.ShiftTime{}
	return c.JSON(shift)
}

// FetchEmployeeCutiDetail handle func
// @Summary Get employee cuti details by employee ID (Mobile)
// @Description Get detailed information about cuti records for a specific employee.
// @Tags cuti
// @Accept json
// @Produce json
// @Param emp_id path int true "Employee ID"
// @Success 200 {object} object "Employee cuti details"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v2/cuti_time_details/{emp_id} [get]
func (ct *CutiHandler) FetchEmployeeCutiDetail(c *fiber.Ctx) error {
	emp_id := c.Params("emp_id")
	empID, _ := strconv.Atoi(emp_id)
	employeeCuti, err := ct.CutiUseCase.FetchEmployeeCutiDetail(empID)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching employee cuti details",
			"error":   err,
		})
	}
	return c.JSON(employeeCuti)
}

// FetchEmpRole handle func
// @Summary Get employee role by employee ID (Mobile)
// @Description Get the role information for a specific employee
// @Tags cuti
// @Accept json
// @Produce json
// @Param emp_id path int true "Employee ID"
// @Success 200 {object} object "Employee role information"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v2/emp_role/{emp_id} [get]
func (ct *CutiHandler) FetchEmpRole(c *fiber.Ctx) error {
	emp_id := c.Params("emp_id")
	empID, _ := strconv.Atoi(emp_id)
	results, err := ct.CutiUseCase.FetchEmpRole(empID)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching employee role",
			"error":   err,
		})
	}
	return c.JSON(results)
}

// FetchAttendance handle func
// @Summary Get employee attendance by outlet, date and employee ID (Mobile)
// @Description Get attendance information for a specific employee at a given outlet on a specific date
// @Tags cuti
// @Accept json
// @Produce json
// @Param outlet_id path int true "Outlet ID"
// @Param date path string true "Date (YYYY-MM-DD)"
// @Param emp_id path int true "Employee ID"
// @Success 200 {object} object "Attendance information"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v2/attendance/{outlet_id}/{date}/{emp_id} [get]
func (ct *CutiHandler) FetchAttendance(c *fiber.Ctx) error {
	outlet_id := c.Params("outlet_id")
	outletID, _ := strconv.Atoi(outlet_id)
	emp_id := c.Params("emp_id")
	empID, _ := strconv.Atoi(emp_id)
	date := c.Params("date")
	results, err := ct.CutiUseCase.FetchAttendance(outletID, date, empID)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching attendance",
			"error":   err,
		})
	}
	return c.JSON(results)
}

// Absensi handle func
// @Summary Record employee attendance (Mobile)
// @Description Record attendance for an employee with details like outlet, time, and code
// @Tags cuti
// @Accept multipart/form-data
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param tip_outlet_id formData string true "Outlet ID"
// @Param tip_nama_karyawan formData string true "Employee name"
// @Param tip_tanggal formData string true "Date (YYYY-MM-DD)"
// @Param tip_jam formData string true "Time (HH:MM:SS)"
// @Param tip_kode formData string true "Attendance code"
// @Param employee_id formData string true "Employee ID"
// @Success 200 {object} fiber.Map "Success response with message and status"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v2/scan_attendance [post]
func (ct *CutiHandler) Absensi(c *fiber.Ctx) error {
	form, err := c.MultipartForm()
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "wrong form key",
			"error":   err,
		})
	}
	outletID := ""
	if form.Value["tip_outlet_id"] != nil {
		outletID = form.Value["tip_outlet_id"][0]
	} else {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"status": 0,
			"error":  "tip outlet id empty",
		})
	}
	namaKaryawan := ""
	if form.Value["tip_nama_karyawan"] != nil {
		namaKaryawan = form.Value["tip_nama_karyawan"][0]
	} else {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"status": 0,
			"error":  "tip nama karyawan empty",
		})
	}
	tanggal := ""
	if form.Value["tip_tanggal"] != nil {
		tanggal = form.Value["tip_tanggal"][0]
	} else {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"status": 0,
			"error":  "tip tanggal empty",
		})
	}
	tipJam := ""
	if form.Value["tip_jam"] != nil {
		tipJam = form.Value["tip_jam"][0]
	} else {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"status": 0,
			"error":  "tip jam empty",
		})
	}
	tipKode := ""
	if form.Value["tip_kode"] != nil {
		tipKode = form.Value["tip_kode"][0]
	} else {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"status": 0,
			"error":  "tip kode empty",
		})
	}
	employeeID := ""
	if form.Value["employee_id"] != nil {
		employeeID = form.Value["employee_id"][0]
	} else {
		c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"status": 0,
			"error":  "employee id empty",
		})
	}

	data := map[string]any{
		"tip_outlet_id":     outletID,
		"tip_nama_karyawan": namaKaryawan,
		"tip_tanggal":       tanggal,
		"tip_jam":           tipJam,
		"tip_kode":          tipKode,
		"employee_id":       employeeID,
	}

	err = ct.CutiUseCase.Absensi(data)
	if err != nil {
		c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"status": 0,
			"error":  err,
		})
	}
	c.Status(fiber.StatusOK).JSON(&fiber.Map{
		"status":  1,
		"message": "successfully add presensi",
	})
	return nil
}

// FetchCutiDetails handle func
// @Summary Get cuti details by transaction ID (Mobile)
// @Description Get detailed information about a specific cuti transaction
// @Tags cuti
// @Accept json
// @Produce json
// @Param trc_id path int true "Transaction Cuti ID"
// @Success 200 {object} object "Cuti details"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v2/fetch_cuti_details/{trc_id} [get]
func (ct *CutiHandler) FetchCutiDetails(c *fiber.Ctx) error {
	trc_id := c.Params("trc_id")
	trcID, _ := strconv.Atoi(trc_id)
	results, err := ct.CutiUseCase.FetchCutiDetails(trcID)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching cuti details",
			"error":   err,
		})
	}
	return c.JSON(results)
}

// FetchOutletLatLing handlefunc
// @Summary Get outlet latitude and longitude (Mobile)
// @Description Get latitude and longitude information for one or more outlets
// @Tags cuti
// @Accept json
// @Produce json
// @Param outlet_id path string true "Outlet ID(s) (comma separated)"
// @Success 200 {object} object "Outlet location information"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v2/fetch_outlet_latlng/{outlet_id} [get]
func (ct *CutiHandler) FetchOutletLatLing(c *fiber.Ctx) error {
	outlet_id := c.Params("outlet_id")
	outletsID := strings.Split(outlet_id, ",")
	result, err := ct.CutiUseCase.FetchOutletLatLing(outletsID)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "fetch outlet latling error",
			"error":   err,
		})
	}
	return c.JSON(result)
}

// FetchEmployeeDetails handle func
// @Summary Get employee details by employee ID (Mobile)
// @Description Get detailed information about a specific employee
// @Tags cuti
// @Accept json
// @Produce json
// @Param emp_id path int true "Employee ID"
// @Success 200 {object} object "Employee details"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v2/fetch_employee_details/{emp_id} [get]
func (ct *CutiHandler) FetchEmployeeDetails(c *fiber.Ctx) error {
	emp_id := c.Params("emp_id")
	empID, _ := strconv.Atoi(emp_id)
	results, err := ct.CutiUseCase.FetchEmployeeDetails(empID)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetch employee details",
			"error":   err,
		})
	}
	return c.JSON(results)
}

// FetchEmpType handle func
// @Summary Get employee type by employee ID (Mobile)
// @Description Get the type information for a specific employee
// @Tags cuti
// @Accept json
// @Produce json
// @Param emp_id path int true "Employee ID"
// @Success 200 {object} object "Employee type information"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v2/fetch_employee_type/{emp_id} [get]
func (ct *CutiHandler) FetchEmpType(c *fiber.Ctx) error {
	emp_id := c.Params("emp_id")
	empID, _ := strconv.Atoi(emp_id)
	res, err := ct.CutiUseCase.FetchEmpType(empID)
	if err != nil {
		fmt.Printf("fetching employee type error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "Fetching employee type error",
			"error":   err,
		})
	}
	return c.JSON(res)
}

// FetchSingleEmployeeCuti handle func
// @Summary Get single employee cuti by transaction ID (Mobile)
// @Description Get detailed information about a specific cuti record for an employee
// @Tags cuti
// @Accept json
// @Produce json
// @Param trc_id path int true "Transaction Cuti ID"
// @Success 200 {object} object "Employee cuti information"
// @Failure 400 {object} fiber.Map "Bad request error"
// @Router /v2/fetch_single_cuti_details/{trc_id} [get]
func (ct CutiHandler) FetchSingleEmployeeCuti(c *fiber.Ctx) error {
	trc_id := c.Params("trc_id")
	trcID, _ := strconv.Atoi(trc_id)
	res, err := ct.CutiUseCase.FetchSingleEmployeeCuti(trcID)
	if err != nil {
		fmt.Printf("fetch cuti error: %v", err)
		return c.Status(fiber.ErrBadRequest.Code).JSON(&fiber.Map{
			"message": "fetch cuti error",
			"status":  fiber.ErrBadRequest,
			"error":   err,
		})
	}
	return c.JSON(res)
}

// FetchCutiV2 godoc
// @Summary Fetch cuti v2
// @Description Fetch cuti v2
// @Tags Cuti
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer {token}"
// @Param outlet_ids query string false "Comma-separated list of outlet IDs"
// @Param status query string false "Comma-separated list of statuses (pending,approved,rejected)"
// @Param start_date query string false "Start date in DD-MM-YYYY format"
// @Param end_date query string false "End date in DD-MM-YYYY format"
// @Success 200 {object} fiber.Map{data=[]domain.CutiV2Detail}
// @Failure 400 {object} fiber.Map
// @Failure 401 {object} fiber.Map
// @Failure 500 {object} fiber.Map
// @Router /cuti/v2 [get]
func (h *CutiHandler) FetchCutiV2(c *fiber.Ctx) error {
	adminIDStr := c.Get("business_id")
	adminID, err := strconv.Atoi(adminIDStr)
	log.IfError(err)

	// Get filters from query parameters
	filter := domain.CutiFilter{
		OutletIDs: c.Query("outlet_ids"),
		Status:    c.Query("status"),
		StartDate: c.Query("start_date"),
		EndDate:   c.Query("end_date"),
	}

	result, err := h.CutiUseCase.FetchCutiV2(adminID, filter)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "error fetching cuti records",
			"error":   err,
		})
	}

	return c.JSON(result)
}
