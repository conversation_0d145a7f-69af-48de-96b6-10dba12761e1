package usecase

import (
	"errors"
	"fmt"
	"time"

	"gitlab.com/backend/api-hrm/config"
	"golang.org/x/crypto/bcrypt"

	"github.com/dgrijalva/jwt-go"
	"gitlab.com/backend/api-hrm/domain"
)

type loginUseCase struct {
	domain.LoginRepository
	auth config.JWTAuth
}

func NewLoginUseCase(repo domain.LoginRepository, auth config.JWTAuth) domain.LoginUseCase {
	return &loginUseCase{repo, auth}
}

func (u *loginUseCase) Login(lg domain.Login) (domain.UserToken, error) {
	var userToken domain.UserToken

	//1.check user from db
	pass, err := u.LoginRepository.FindPassword(lg.Email)
	if err != nil {
		fmt.Println(err)
	}

	if pass == "" {
		fmt.Println("no user found with email: ", lg.Email)
		return userToken, errors.New("invalid email or password")
	}

	//2. validate password...
	err = bcrypt.CompareHashAndPassword([]byte(pass), []byte(lg.Password))
	if err != nil {
		fmt.Println(lg.Email, "password not match")
		return userToken, errors.New("invalid email or password")
	}

	//get user detail
	user, err := u.FindUserByEmail(lg.Email)
	if err != nil {
		return userToken, err
	}

	//3. if valid, then create token
	token := jwt.New(u.auth.Method)
	expired := time.Now().Add(time.Hour * 72).Unix()
	claims := token.Claims.(jwt.MapClaims)
	claims["exp"] = expired
	claims["role"] = "{}"
	//claims["uid"] = user.UserId

	// Generate encoded token and send it as response.
	t, err := token.SignedString(u.auth.PrivateKey)

	userToken.User = user
	userToken.Token = domain.Token{
		Token:   t,
		Expired: expired,
		Type:    "Bearer",
	}

	return userToken, err
}
