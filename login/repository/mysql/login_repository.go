package mysql

import (
	"database/sql"
	"gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"gitlab.com/backend/api-hrm/domain"
)

type mysqlLoginRepository struct {
	mysql.Repository
}

func NewMysqlLoginRepository(conn *sql.DB) domain.LoginRepository {
	return &mysqlLoginRepository{mysql.Repository{
		Conn: conn,
	}}
}

func (r *mysqlLoginRepository) FindPassword(email string) (string, error) {
	result, err := r.Repository.Query("select password from admin where email = ? limit 1", email).Map()
	return cast.ToString(result["password"]), err
}

func (r *mysqlLoginRepository) FindUserByEmail(email string) (domain.User, error) {
	var user domain.User
	err := r.Query("select * from admin where email = ? limit 1", email).Model(&user)
	return user, err
}
