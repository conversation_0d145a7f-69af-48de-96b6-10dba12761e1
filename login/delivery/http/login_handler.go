package http

import (
	"github.com/gofiber/fiber/v2"
	"gitlab.com/backend/api-hrm/domain"
)

type loginHandler struct {
	domain.LoginUseCase
}

func NewLoginHandler(app *fiber.App, uc domain.LoginUseCase) {
	handler := &loginHandler{uc}

	app.Post("/authorization", handler.<PERSON>gin)
}

func (h *loginHandler) Login(c *fiber.Ctx) error {
	user, err := h.LoginUseCase.Login(domain.Login{
		Email:    c.FormValue("email"),
		Password: c.<PERSON>alue("password"),
	})

	if err != nil {
		return err
	}

	return c.<PERSON>(user)
}
