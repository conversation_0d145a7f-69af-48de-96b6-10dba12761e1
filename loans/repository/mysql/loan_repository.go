package mysql

import (
	"database/sql"
	"time"

	"gitlab.com/backend/api-hrm/core/log"
	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLLoanRepository struct {
	mysql.Repository
}

// NewMySQLLoanRepository creates a new MySQL loan repository
func NewMySQLLoanRepository(conn *sql.DB) domain.LoanRepository {
	return &mySQLLoanRepository{mysql.Repository{Conn: conn}}
}

// FetchLoanTypes retrieves all loan types filtered by admin_fkid
func (m *mySQLLoanRepository) FetchLoanTypes(adminFkid int) ([]domain.LoanType, error) {
	query := `SELECT *
	FROM hrm_loan_types
	WHERE admin_fkid = ?
	ORDER BY created_at DESC`

	var loanTypes []domain.LoanType
	err := m.Query(query, adminFkid).Model(&loanTypes)
	if log.IfError(err) {
		return nil, err
	}

	return loanTypes, nil
}

// FindOrCreateLoanType finds an existing loan type by name or creates a new one
func (m *mySQLLoanRepository) FindOrCreateLoanType(loan domain.LoanRequest, adminFkid int) (int64, error) {
	// First, try to find existing loan type
	query := `SELECT loan_type_id FROM hrm_loan_types WHERE loan_type_name = ? AND admin_fkid = ? LIMIT 1`
	result, err := m.Query(query, loan.LoanTypeName, adminFkid).Map()
	if err == nil && len(result) > 0 {
		// Found existing loan type
		if typeID, exists := result["loan_type_id"]; exists {
			return int64(typeID.(int64)), nil
		}
	}

	// Create new loan type with default values
	now := time.Now().UnixMilli() // You might want to use time.Now().UnixMilli()
	loanTypeData := map[string]any{
		"loan_type_name": loan.LoanTypeName,
		"principal":      loan.Principal,
		"interest_rate":  loan.InterestRate,
		"term_months":    loan.TermMonths,
		"admin_fkid":     adminFkid,
		"created_at":     now,
		"updated_at":     now,
	}

	_, loanTypeID, err := m.InsertGetLastID("hrm_loan_types", loanTypeData)
	if err != nil {
		log.IfError(err)
		return 0, err
	}

	return loanTypeID, nil
}

// AddLoan creates a new loan record
func (m *mySQLLoanRepository) AddLoan(loan domain.LoanRequest, adminFkid int) (int64, error) {
	var loanID int64

	err := m.WithTransaction(func(tx mysql.Transaction) error {
		// Find or create loan type
		loanTypeFkid, err := m.FindOrCreateLoanType(loan, adminFkid)
		if err != nil {
			return err
		}

		// Calculate total_due: principal + (principal * interest_rate)
		totalDue := loan.Principal + (loan.Principal * loan.InterestRate)

		// Prepare loan data
		now := time.Now().UnixMilli() // You might want to use time.Now().UnixMilli()
		loanData := map[string]interface{}{
			"hrm_employee_fkid": loan.HrmEmployeeFkid,
			"loan_type_fkid":    loanTypeFkid,
			"principal":         loan.Principal,
			"interest_rate":     loan.InterestRate,
			"term_months":       loan.TermMonths,
			"first_due_date":    loan.FirstDueDate,
			"total_due":         totalDue,
			"outstanding_total": totalDue, // Initially same as total_due
			"notes":             loan.Notes,
			"status":            "active",
			"created_at":        now,
			"updated_at":        now,
		}

		// Insert loan record
		_, id, err := m.InsertGetLastID("hrm_loans", loanData)
		if err != nil {
			return err
		}

		loanID = id
		return nil
	})

	if err != nil {
		log.IfError(err)
		return 0, err
	}

	return loanID, nil
}
