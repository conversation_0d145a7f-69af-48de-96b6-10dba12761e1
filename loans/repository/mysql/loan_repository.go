package mysql

import (
	"database/sql"
	"encoding/json"

	"gitlab.com/backend/api-hrm/core/log"
	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLLoanRepository struct {
	mysql.Repository
}

// NewMySQLLoanRepository creates a new MySQL loan repository
func NewMySQLLoanRepository(conn *sql.DB) domain.LoanRepository {
	return &mySQLLoanRepository{mysql.Repository{Conn: conn}}
}

// FetchLoanTypes retrieves all loan types filtered by admin_fkid
func (m *mySQLLoanRepository) FetchLoanTypes(adminFkid int) ([]domain.LoanType, error) {
	query := `SELECT 
		loan_type_id,
		loan_type_name,
		principal,
		interest_rate,
		term_months,
		admin_fkid,
		created_at,
		updated_at
	FROM hrm_loan_types 
	WHERE admin_fkid = ?
	ORDER BY created_at DESC`

	results, err := m.QueryArrayOld(query, adminFkid)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	resultJSON, err := json.Marshal(results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var loanTypes []domain.LoanType
	err = json.Unmarshal(resultJSON, &loanTypes)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	return loanTypes, nil
}
