package http

import (
	"github.com/gofiber/fiber/v2"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/domain"
)

// LoanHandler struct
type Loan<PERSON>andler struct {
	LoanUseCase domain.LoanUseCase
}

// NewLoanHandler creates a new loan handler and sets up routes
func NewLoanHandler(app *fiber.App, uc domain.LoanUseCase) {
	handler := &LoanHandler{LoanUseCase: uc}

	// API routes
	v1 := app.Group("/v1")
	v1.Get("/loan-types", handler.FetchLoanTypes)
	v1.Post("/loans", handler.AddLoan)
}

// FetchLoanTypes retrieves all loan types for the authenticated user
// @Summary Get all loan types
// @Description Get all loan types filtered by the authenticated user's business ID (admin_fkid)
// @Tags loans
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {array} domain.LoanType "List of loan types"
// @Failure 401 {object} fiber.Map "Unauthorized - invalid session"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v1/loan-types [get]
func (l *LoanHandler) FetchLoanTypes(c *fiber.Ctx) error {
	// Get user session to extract admin_fkid
	user := domain.GetUserSessionFiber(c)
	if user.BusinessId == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(&fiber.Map{
			"message": "Unauthorized - invalid session",
			"status":  0,
		})
	}

	// Fetch loan types filtered by admin_fkid (user.BusinessId)
	loanTypes, err := l.LoanUseCase.FetchLoanTypes(user.BusinessId)
	if err != nil {
		log.IfError(err)
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "error fetching loan types",
			"status":  0,
			"error":   err.Error(),
		})
	}

	return c.JSON(loanTypes)
}

// AddLoan creates a new loan record
// @Summary Create a new loan
// @Description Create a new loan record with automatic loan type lookup/creation and total_due calculation. Validates that first_due_date is not in the past and follows YYYY-MM-DD format.
// @Tags loans
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param loan body domain.LoanRequest true "Loan information (first_due_date must be YYYY-MM-DD format and not in the past)"
// @Success 201 {object} fiber.Map "Successfully created loan with loan ID"
// @Failure 400 {object} fiber.Map "Bad request - validation error (e.g., first due date in the past)"
// @Failure 401 {object} fiber.Map "Unauthorized - invalid session"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v1/loans [post]
func (l *LoanHandler) AddLoan(c *fiber.Ctx) error {
	// Get user session to extract admin_fkid
	user := domain.GetUserSessionFiber(c)
	if user.BusinessId == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(&fiber.Map{
			"message": "Unauthorized - invalid session",
			"status":  0,
		})
	}

	// Parse request body
	var loanRequest domain.LoanRequest
	if err := c.BodyParser(&loanRequest); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "Invalid request body",
			"status":  0,
			"error":   err.Error(),
		})
	}

	// Create the loan
	loanID, err := l.LoanUseCase.AddLoan(loanRequest, user.BusinessId)
	if err != nil {
		log.IfError(err)
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "Failed to create loan",
			"status":  0,
			"error":   err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(&fiber.Map{
		"message": "Loan created successfully",
		"status":  1,
		"loan_id": loanID,
	})
}
