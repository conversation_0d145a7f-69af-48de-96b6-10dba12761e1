package http

import (
	"github.com/gofiber/fiber/v2"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/domain"
)

// LoanHandler struct
type Loan<PERSON>andler struct {
	LoanUseCase domain.LoanUseCase
}

// NewLoanHandler creates a new loan handler and sets up routes
func NewLoanHandler(app *fiber.App, uc domain.LoanUseCase) {
	handler := &LoanHandler{LoanUseCase: uc}

	// API routes
	v1 := app.Group("/v1")
	v1.Get("/loan-types", handler.FetchLoanTypes)
}

// FetchLoanTypes retrieves all loan types for the authenticated user
// @Summary Get all loan types
// @Description Get all loan types filtered by the authenticated user's business ID (admin_fkid)
// @Tags loans
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {array} domain.LoanType "List of loan types"
// @Failure 401 {object} fiber.Map "Unauthorized - invalid session"
// @Failure 500 {object} fiber.Map "Internal server error"
// @Router /v1/loan-types [get]
func (l *<PERSON><PERSON><PERSON><PERSON><PERSON>) FetchLoanTypes(c *fiber.Ctx) error {
	// Get user session to extract admin_fkid
	user := domain.GetUserSessionFiber(c)
	if user.BusinessId == 0 {
		return c.Status(fiber.StatusUnauthorized).JSON(&fiber.Map{
			"message": "Unauthorized - invalid session",
			"status":  0,
		})
	}

	// Fetch loan types filtered by admin_fkid (user.BusinessId)
	loanTypes, err := l.LoanUseCase.FetchLoanTypes(user.BusinessId)
	if err != nil {
		log.IfError(err)
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "error fetching loan types",
			"status":  0,
			"error":   err.Error(),
		})
	}

	return c.JSON(loanTypes)
}
