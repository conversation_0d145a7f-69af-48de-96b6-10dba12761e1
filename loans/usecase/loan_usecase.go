package usecase

import (
	"gitlab.com/backend/api-hrm/domain"
)

type loanUseCase struct {
	loanRepository domain.LoanRepository
}

// NewLoanUseCase creates a new loan use case
func NewLoanUseCase(lr domain.LoanRepository) domain.LoanUseCase {
	return &loanUseCase{loanRepository: lr}
}

// FetchLoanTypes retrieves all loan types for the given admin
func (l *loanUseCase) FetchLoanTypes(adminFkid int) ([]domain.LoanType, error) {
	return l.loanRepository.FetchLoanTypes(adminFkid)
}
