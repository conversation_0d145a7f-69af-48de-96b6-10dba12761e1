package usecase

import (
	"fmt"

	"gitlab.com/backend/api-hrm/domain"
)

type loanUseCase struct {
	loanRepository domain.LoanRepository
}

// NewLoanUseCase creates a new loan use case
func NewLoanUseCase(lr domain.LoanRepository) domain.LoanUseCase {
	return &loanUseCase{loanRepository: lr}
}

// FetchLoanTypes retrieves all loan types for the given admin
func (l *loanUseCase) FetchLoanTypes(adminFkid int) ([]domain.LoanType, error) {
	return l.loanRepository.FetchLoanTypes(adminFkid)
}

// AddLoan creates a new loan with business logic validation
func (l *loanUseCase) AddLoan(loan domain.LoanRequest, adminFkid int) (int64, error) {
	// Business logic validation can be added here
	// For example: validate employee exists, validate date format, etc.

	// Validate first_due_date format (should be YYYY-MM-DD)
	// You could add date parsing validation here if needed

	// Validate that principal and interest_rate are reasonable
	if loan.Principal <= 0 {
		return 0, fmt.Errorf("principal must be greater than 0")
	}

	if loan.InterestRate < 0 {
		return 0, fmt.Errorf("interest rate cannot be negative")
	}

	if loan.TermMonths <= 0 {
		return 0, fmt.Errorf("term months must be greater than 0")
	}

	// Call repository to create the loan
	return l.loanRepository.AddLoan(loan, adminFkid)
}
