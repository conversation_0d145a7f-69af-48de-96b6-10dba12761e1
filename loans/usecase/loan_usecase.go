package usecase

import (
	"fmt"
	"time"

	"gitlab.com/backend/api-hrm/domain"
)

type loanUseCase struct {
	loanRepository domain.LoanRepository
}

// NewLoanUseCase creates a new loan use case
func NewLoanUseCase(lr domain.LoanRepository) domain.LoanUseCase {
	return &loanUseCase{loanRepository: lr}
}

// FetchLoanTypes retrieves all loan types for the given admin
func (l *loanUseCase) FetchLoanTypes(adminFkid int) ([]domain.LoanType, error) {
	return l.loanRepository.FetchLoanTypes(adminFkid)
}

// AddLoan creates a new loan with business logic validation
func (l *loanUseCase) AddLoan(loan domain.LoanRequest, adminFkid int) (int64, error) {
	// Business logic validation can be added here
	// For example: validate employee exists, validate date format, etc.

	// Validate first_due_date format (should be YYYY-MM-DD)
	// You could add date parsing validation here if needed
	if loan.FirstDueDate == "" {
		return 0, fmt.Errorf("first due date cannot be empty")
	}
	if len(loan.FirstDueDate) != 10 {
		return 0, fmt.Errorf("first due date format is invalid")
	}
	if loan.FirstDueDate[4:5] != "-" || loan.FirstDueDate[7:8] != "-" {
		return 0, fmt.Errorf("first due date format is invalid")
	}

	// Parse the first due date and validate it's not in the past
	dueDate, err := time.Parse("2006-01-02", loan.FirstDueDate)
	if err != nil {
		return 0, fmt.Errorf("first due date format is invalid: %v", err)
	}

	// Get current date (without time component for comparison)
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// Check if due date is in the past
	if dueDate.Before(today) {
		return 0, fmt.Errorf("first due date cannot be in the past")
	}

	// Validate that principal and interest_rate are reasonable
	if loan.Principal <= 0 {
		return 0, fmt.Errorf("principal must be greater than 0")
	}

	if loan.InterestRate < 0 {
		return 0, fmt.Errorf("interest rate cannot be negative")
	}

	if loan.TermMonths <= 0 {
		return 0, fmt.Errorf("term months must be greater than 0")
	}

	// Call repository to create the loan
	return l.loanRepository.AddLoan(loan, adminFkid)
}
