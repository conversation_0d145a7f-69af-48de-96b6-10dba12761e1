package mysql

import (
	"database/sql"
	"encoding/json"
	"html"

	"gitlab.com/backend/api-hrm/core/log"

	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

// mySqlTypeRepository struct
type mySQLTypeRepository struct {
	mysql.Repository
}

// NewMySQLTypeRepository type
func NewMySQLTypeRepository(conn *sql.DB) domain.TypeRepository {
	return &mySQLTypeRepository{mysql.Repository{Conn: conn}}
}

func (m *mySQLTypeRepository) FetchAll(id int) ([]domain.HrmType, error) {
	results, err := m.QueryArrayOld("select type_id as ACTION, admin_fkid, type_name as TYPE_NAME, type_hours as TYPE_HOURS, rest_hours as REST_HOURS from hrm_employee_type where admin_fkid=? ORDER BY type_id DESC", id)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	resultsJSON, err := json.Marshal(results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var result []domain.HrmType
	var newResult []domain.HrmType
	err = json.Unmarshal(resultsJSON, &result)
	for k, v := range result {
		if v.No == 0 {
			v.No = k + 1
			newResult = append(newResult, v)
		}
	}

	return newResult, err
}

func (m *mySQLTypeRepository) Fetch() ([]domain.HrmMasterType, error) {
	results, err := m.QueryArrayOld("select * from hrm_employee_type")
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resultsJSON, err := json.Marshal(results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var result []domain.HrmMasterType
	err = json.Unmarshal(resultsJSON, &result)
	return result, err
}

// FetchByID func
func (m *mySQLTypeRepository) FetchByID(id int64) (domain.HrmMasterType, error) {
	var result domain.HrmMasterType
	err := m.Query("select * from hrm_employee_type where type_id = ?", id).Model(&result)
	return result, err
}

func (m *mySQLTypeRepository) Add(types domain.HrmMasterType, adminID int) error {
	_, err := m.Insert("hrm_employee_type", map[string]interface{}{
		"admin_fkid": adminID,
		"type_name":  html.EscapeString(types.TypeName),
		"type_hours": types.TypeHours,
		"rest_hours": types.RestHours,
	})

	return err
}

func (m *mySQLTypeRepository) Update(types domain.HrmMasterType, adminID int) error {
	_, err := m.Updates("hrm_employee_type", map[string]interface{}{
		"type_name":  types.TypeName,
		"type_hours": types.TypeHours,
		"rest_hours": types.RestHours,
	}, map[string]interface{}{
		"type_id":    types.TypeID,
		"admin_fkid": adminID,
	})
	return err
}

func (m *mySQLTypeRepository) Delete(types domain.HrmMasterType) error {
	_, err := m.Deletes("hrm_employee_type", map[string]interface{}{
		"type_id": types.TypeID,
	})
	return err
}
