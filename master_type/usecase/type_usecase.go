package usecase

import (
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/domain"
)

type typeUseCase struct {
	typeRepository domain.TypeRepository
}

// NewTypeUseCase func
func NewTypeUseCase(t domain.TypeRepository) domain.TypeUseCase {
	return &typeUseCase{typeRepository: t}
}

func (t *typeUseCase) FetchAll(id int) ([]domain.HrmType, error) {
	return t.typeRepository.FetchAll(id)
}

func (t *typeUseCase) Fetch() ([]domain.HrmMasterType, error) {
	result, err := t.typeRepository.Fetch()
	if err != nil {
		return nil, err
	}
	for i, v := range result {
		result[i].RestMinutes = v.RestHours % 60
		result[i].RestHours = v.RestHours / 60
	}
	return result, nil
}

func (t *typeUseCase) FetchByID(id int64) (domain.HrmMasterType, error) {
	result, err := t.typeRepository.FetchByID(id)
	if err != nil {
		return domain.HrmMasterType{}, err
	}
	//adjust the rest hour
	log.Info("rest hour: %v", result.RestHours)
	result.RestMinutes = result.RestHours % 60
	result.RestHours = result.RestHours / 60
	return result, nil
}

func (t *typeUseCase) Add(types domain.HrmMasterType, adminID int) error {
	//adjust the rest hour
	types.RestHours = types.RestMinutes + (types.RestHours * 60)
	return t.typeRepository.Add(types, adminID)
}

func (t *typeUseCase) Update(types domain.HrmMasterType, adminID int) error {
	types.RestHours = types.RestMinutes + (types.RestHours * 60)
	return t.typeRepository.Update(types, adminID)
}

func (t *typeUseCase) Delete(types domain.HrmMasterType) error {
	return t.typeRepository.Delete(types)
}
