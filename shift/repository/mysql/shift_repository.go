package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/cast"

	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

// mysql.MySQLShiftRepository shift repository struct
type MySQLShiftRepository struct {
	mysql.Repository
}

// NewSQLShiftRepository func
func NewSQLShiftRepository(conn *sql.DB) domain.ShiftRepository {
	return &MySQLShiftRepository{mysql.Repository{Conn: conn}}
}

// Fetch get all shift
func (m *MySQLShiftRepository) Fetch(adminFkid int) ([]domain.MasterShift, error) {
	query := `SELECT ms.shift_id AS shift_id, ms.shift_code, ms.shift_name,
	s.name AS shift_name_id, s.shift_id AS shift_id_name, DATE_FORMAT(ms.shift_in, '%H:%i') AS shift_in,
	DATE_FORMAT(ms.shift_out, '%H:%i') AS shift_out, ms.shift_tolerance, ms.shift_color, ms.shift_active,
	et.type_name AS shift_type, et.type_id AS shift_type_id, ot.outlet_id AS shift_office_id,
	ot.name AS shift_office, DATE_FORMAT( ADDTIME( ms.shift_in, CONCAT('00:0', ms.shift_tolerance) ), '%H:%i' ) AS shift_max_in
	FROM hrm_master_shift AS ms
	JOIN hrm_employee_type et ON et.type_id = ms.shift_type AND et.admin_fkid = @adminId
	JOIN shift s ON s.shift_id = ms.shift_fkid AND s.admin_fkid = @adminId
	JOIN outlets ot ON ot.outlet_id = ms.shift_office
	ORDER BY ms.shift_office, DATE_FORMAT(ms.shift_in, '%H:%i') DESC`

	query, params := mysql.MapParam(query, map[string]any{
		"adminId": adminFkid,
	})
	var result []domain.MasterShift
	err := m.Query(query, params...).Model(&result)
	return result, err
}

// FetchWithFilter get all shift with filters
func (m *MySQLShiftRepository) FetchWithFilter(adminFkid int, filter domain.ShiftFilter) ([]domain.MasterShift, error) {
	query := `SELECT ms.shift_id AS shift_id, ms.shift_code, ms.shift_name,
	s.name AS shift_name_id, s.shift_id AS shift_id_name, DATE_FORMAT(ms.shift_in, '%H:%i') AS shift_in,
	DATE_FORMAT(ms.shift_out, '%H:%i') AS shift_out, ms.shift_tolerance, ms.shift_color, ms.shift_active,
	et.type_name AS shift_type, et.type_id AS shift_type_id, ot.outlet_id AS shift_office_id,
	ot.name AS shift_office, DATE_FORMAT( ADDTIME( ms.shift_in, CONCAT('00:0', ms.shift_tolerance) ), '%H:%i' ) AS shift_max_in
	FROM hrm_master_shift AS ms
	JOIN hrm_employee_type et ON et.type_id = ms.shift_type AND et.admin_fkid = @adminId
	JOIN shift s ON s.shift_id = ms.shift_fkid AND s.admin_fkid = @adminId
	JOIN outlets ot ON ot.outlet_id = ms.shift_office`

	params := map[string]any{
		"adminId": adminFkid,
	}

	// Add outlet filter if provided
	if len(filter.OutletIDs) > 0 {
		query += ` WHERE ot.outlet_id IN (@outletIds)`
		params["outletIds"] = filter.OutletIDs
	}

	query += ` ORDER BY ms.shift_office, DATE_FORMAT(ms.shift_in, '%H:%i') DESC`

	query, paramSlice := mysql.MapParam(query, params)
	var result []domain.MasterShift
	err := m.Query(query, paramSlice...).Model(&result)
	return result, err
}

// FetchShiftGroup get shift group
func (m *MySQLShiftRepository) FetchShiftGroup(outletFkid []interface{}) ([]domain.ShiftGroup, error) {
	query := `SELECT s.shift_id AS id, s.name AS shift_name, so.shift_fkid, so.outlet_fkid
	FROM shift AS s
	JOIN shift_outlet AS so ON so.shift_fkid = s.shift_id $WHERE
	GROUP BY s.name,id,so.outlet_fkid`

	where := " WHERE so.outlet_fkid IN @outletIds "
	if len(outletFkid) == 0 {
		where = ""
	}
	query, args := mysql.MapParam(query, map[string]any{
		"outletIds": outletFkid,
		"WHERE":     where,
	})
	var result []domain.ShiftGroup
	err := m.Query(query, args...).Model(&result)
	// log.Info("FetchShiftGroup: %v, result: %v, sql: %v", err, cast.ToString(result), query)
	return result, err
}

// Add shift bulk
func (m *MySQLShiftRepository) Add(shifts []map[string]interface{}) error {
	_, err := m.BulkInsert("hrm_master_shift", shifts)
	return err
}

// Update shift
func (m *MySQLShiftRepository) Update(shift []map[string]interface{}) error {
	_, err := m.SingleUpdate("hrm_master_shift", "shift_id", shift)
	return err
}

// Delete shift
func (m *MySQLShiftRepository) Delete(shift domain.ShiftAdd) error {
	_, err := m.Deletes("hrm_master_shift", map[string]interface{}{
		"shift_id": shift.ShiftID,
	})
	return err
}

// UpdateHot update hot
func (m *MySQLShiftRepository) UpdateHot(shifts []map[string]interface{}) error {
	err := m.BulkUpdate("hrm_master_shift", "shift_id", shifts)
	return err
}

// FetchShiftByName fetches shifts by name for validation
func (m *MySQLShiftRepository) FetchShiftByName(adminID int, shiftNames []string) ([]domain.ShiftValidation, error) {
	if len(shiftNames) == 0 {
		log.Info("No shift names to fetch")
		return []domain.ShiftValidation{}, nil
	}

	query := `SELECT name, shift_id as id FROM shift WHERE name IN @names AND admin_fkid = @adminId `
	query, params := mysql.MapParam(query, map[string]any{
		"names":   shiftNames,
		"adminId": adminID,
	})

	var validShifts []domain.ShiftValidation
	err := m.Query(query, params...).Model(&validShifts)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	return validShifts, nil
}

// FetchTypeByName fetches employee types by name for validation
func (m *MySQLShiftRepository) FetchTypeByName(adminID int, typeNames []string) ([]domain.TypeValidation, error) {
	if len(typeNames) == 0 {
		return []domain.TypeValidation{}, nil
	}

	// Build placeholders for IN clause
	placeholders := strings.Repeat("?,", len(typeNames))
	if len(placeholders) > 0 {
		placeholders = placeholders[:len(placeholders)-1] // Remove trailing comma
	}

	// Build query
	query := fmt.Sprintf("SELECT type_name, type_id FROM hrm_employee_type WHERE type_name IN (%s) AND admin_fkid = ?", placeholders)

	// Prepare arguments
	args := make([]interface{}, 0, len(typeNames)+1)
	for _, name := range typeNames {
		args = append(args, name)
	}
	args = append(args, adminID)

	// Execute query
	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		log.IfError(err)
		return nil, fmt.Errorf("error querying type names: %v", err)
	}

	// Convert to JSON and then to struct
	resultJSON, err := json.Marshal(results)
	if err != nil {
		log.IfError(err)
		return nil, fmt.Errorf("error marshaling type data: %v", err)
	}

	var validTypes []domain.TypeValidation
	err = json.Unmarshal(resultJSON, &validTypes)
	if err != nil {
		log.IfError(err)
		return nil, fmt.Errorf("error unmarshaling type data: %v", err)
	}

	return validTypes, nil
}

// ImportShifts imports shifts from Excel file
func (m *MySQLShiftRepository) ImportShifts(outletIDs []int, shifts []domain.ShiftImportWithId, adminID int) error {
	log.Info("Importing %d shifts for %d outlets, data: %v", len(shifts), len(outletIDs), cast.ToString(shifts))
	// Begin a transaction
	tx, err := m.Conn.Begin()
	if err != nil {
		log.IfError(err)
		return err
	}

	// Defer rollback in case of error
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Prepare bulk insert data
	bulkInsertData := make([]map[string]any, 0, len(shifts)*len(outletIDs))

	// For each shift and outlet combination, create a data entry
	for _, shift := range shifts {
		for _, outletID := range outletIDs {
			// Prepare data for insertion
			shiftData := map[string]any{
				"shift_office":    outletID,
				"shift_type":      shift.TypeID,
				"shift_code":      shift.ShiftCode,
				"shift_fkid":      shift.ShiftID,
				"shift_in":        shift.TimeIn,
				"shift_out":       shift.TimeOut,
				"shift_tolerance": shift.ShiftTolerance,
			}

			// Handle color field - default to white if not provided or invalid
			if shift.ShiftColor != "" && strings.HasPrefix(shift.ShiftColor, "#") {
				shiftData["shift_color"] = shift.ShiftColor
			} else {
				shiftData["shift_color"] = "#FFFFFF" // Default white color
			}

			// Handle active field - default to active (1) if not provided or invalid
			if strings.ToLower(shift.ShiftActive) == "no" {
				shiftData["shift_active"] = 0 // Inactive
			} else {
				shiftData["shift_active"] = 1 // Active by default
			}

			bulkInsertData = append(bulkInsertData, shiftData)
		}
	}

	// Perform bulk insert
	query, args := mysql.BuildBulkInsertQuery("hrm_master_shift", bulkInsertData)
	result, err := tx.Exec(query, args...)
	if log.IfError(err) {
		log.Info("hrm_master_shift, bulk insert failed, query: %v, args: %v", query, args)
		log.Info("hrm_master_shift, bulk insert failed, data: %v", cast.ToString(bulkInsertData))
		return fmt.Errorf("failed to insert shifts: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.IfError(err)
	}
	log.Info("hrm_master_shift, bulk insert completed, rows affected: %v", rowsAffected)

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		log.IfError(err)
		return err
	}

	return nil
}

// GetShiftNamesForDropdown gets all shift names for dropdown
func (m *MySQLShiftRepository) GetShiftNamesForDropdown(adminID int) ([]string, error) {
	query := "SELECT name FROM shift WHERE admin_fkid = ? ORDER BY name"
	rows, err := m.QueryArrayOld(query, adminID)
	if err != nil {
		return nil, err
	}

	var names []string
	for _, row := range rows {
		if name, ok := row["name"].(string); ok {
			names = append(names, name)
		}
	}
	return names, nil
}

// GetTypeNamesForDropdown gets all type names for dropdown
func (m *MySQLShiftRepository) GetTypeNamesForDropdown(adminID int) ([]string, error) {
	query := "SELECT type_name FROM hrm_employee_type WHERE admin_fkid = ? ORDER BY type_name"
	rows, err := m.QueryArrayOld(query, adminID)
	if err != nil {
		return nil, err
	}

	var names []string
	for _, row := range rows {
		if name, ok := row["type_name"].(string); ok {
			names = append(names, name)
		}
	}
	return names, nil
}
