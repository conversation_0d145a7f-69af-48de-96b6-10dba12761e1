package usecase

import (
	"fmt"
	"strings"

	"github.com/xuri/excelize/v2"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"gitlab.com/backend/api-hrm/domain"
)

type shiftUseCase struct {
	shiftRepository domain.ShiftRepository
}

// NewShiftUseCase get all shifts
func NewShiftUseCase(s domain.ShiftRepository) domain.ShiftUseCase {
	return &shiftUseCase{shiftRepository: s}
}

// FetchShiftByName fetches shifts by name for validation
func (s *shiftUseCase) FetchShiftByName(adminID int, shiftNames []string) ([]domain.ShiftValidation, error) {
	return s.shiftRepository.FetchShiftByName(adminID, shiftNames)
}

// FetchTypeByName fetches employee types by name for validation
func (s *shiftUseCase) FetchTypeByName(adminID int, typeNames []string) ([]domain.TypeValidation, error) {
	return s.shiftRepository.FetchTypeByName(adminID, typeNames)
}

func (s *shiftUseCase) Fetch(adminFkid int) ([]domain.MasterShift, error) {
	return s.shiftRepository.Fetch(adminFkid)
}

func (s *shiftUseCase) FetchWithFilter(adminFkid int, filter domain.ShiftFilter) ([]domain.MasterShift, error) {
	return s.shiftRepository.FetchWithFilter(adminFkid, filter)
}

func (s *shiftUseCase) FetchShiftGroup(outletFkid []interface{}) ([]domain.ShiftGroup, error) {
	return s.shiftRepository.FetchShiftGroup(outletFkid)
}

func (s *shiftUseCase) Add(shifts []map[string]interface{}) error {
	return s.shiftRepository.Add(shifts)
}

// ImportShifts validates and imports shifts from Excel file
func (s *shiftUseCase) ImportShifts(outletIDs []int, shifts []domain.ShiftImport, adminID int) error {
	if len(shifts) == 0 {
		return fmt.Errorf("no shifts to import")
	}

	log.Info("Importing %d shifts for %d outlets", len(shifts), len(outletIDs))

	// Basic validation for required fields
	for i, shift := range shifts {
		// Validate shift name
		if shift.ShiftName == "" {
			return fmt.Errorf("row %d: shift name cannot be empty", i+2) // +2 for header row and 0-indexing
		}

		// Validate shift type
		if shift.TypeName == "" {
			return fmt.Errorf("row %d: shift type cannot be empty", i+2)
		}

		// Validate time format
		if shift.TimeIn == "" {
			return fmt.Errorf("row %d: time in cannot be empty", i+2)
		}

		if shift.TimeOut == "" {
			return fmt.Errorf("row %d: time out cannot be empty", i+2)
		}

		// Validate color format if provided
		if shift.ShiftColor != "" && !strings.HasPrefix(shift.ShiftColor, "#") {
			return fmt.Errorf("row %d: color must start with # (e.g., #FFFFFF)", i+2)
		}

		// Validate active value if provided
		if shift.ShiftActive != "" &&
			strings.ToLower(shift.ShiftActive) != "yes" &&
			strings.ToLower(shift.ShiftActive) != "no" {
			return fmt.Errorf("row %d: active must be 'yes' or 'no'", i+2)
		}
	}

	// Extract all unique shift names and types for batch validation
	var uniqueShiftNames []string
	var uniqueTypeNames []string
	shiftNameMap := make(map[string]bool)
	typeNameMap := make(map[string]bool)

	for _, shift := range shifts {
		if !shiftNameMap[shift.ShiftName] {
			uniqueShiftNames = append(uniqueShiftNames, shift.ShiftName)
			shiftNameMap[shift.ShiftName] = true
		}
		if !typeNameMap[shift.TypeName] {
			uniqueTypeNames = append(uniqueTypeNames, shift.TypeName)
			typeNameMap[shift.TypeName] = true
		}
	}
	log.Info("Unique shift names: %v", uniqueShiftNames)
	log.Info("Unique type names: %v", uniqueTypeNames)

	// Fetch valid shift names and types from database
	validShifts, err := s.shiftRepository.FetchShiftByName(adminID, uniqueShiftNames)
	if err != nil {
		return fmt.Errorf("error fetching shift names: %v", err)
	}

	validTypes, err := s.shiftRepository.FetchTypeByName(adminID, uniqueTypeNames)
	if err != nil {
		return fmt.Errorf("error fetching type names: %v", err)
	}

	// Create maps for quick lookup
	validShiftMap := make(map[string]int)
	for _, shift := range validShifts {
		validShiftMap[shift.Name] = shift.ID
	}

	validTypeMap := make(map[string]int)
	for _, typ := range validTypes {
		validTypeMap[typ.TypeName] = typ.TypeID
	}

	// Validate that all shift names and types exist
	for i, shift := range shifts {
		if _, exists := validShiftMap[shift.ShiftName]; !exists {
			return fmt.Errorf("row %d: shift name '%s' not found", i+2, shift.ShiftName)
		}

		if _, exists := validTypeMap[shift.TypeName]; !exists {
			return fmt.Errorf("row %d: shift type '%s' not found", i+2, shift.TypeName)
		}
	}

	log.Info("validShift: %v, validType: %v", cast.ToString(validShiftMap), cast.ToString(validTypeMap))

	// Create a new slice of shifts with the IDs
	shiftsWithIDs := make([]domain.ShiftImportWithId, len(shifts))
	for i, shift := range shifts {
		shiftsWithIDs[i].ShiftImport = shift
		shiftsWithIDs[i].ShiftID = validShiftMap[shift.ShiftName]
		shiftsWithIDs[i].TypeID = validTypeMap[shift.TypeName]
	}

	// All validations passed, proceed with import
	return s.shiftRepository.ImportShifts(outletIDs, shiftsWithIDs, adminID)
}

func (s *shiftUseCase) Update(shift []map[string]interface{}) error {
	return s.shiftRepository.Update(shift)
}

func (s *shiftUseCase) Delete(shift domain.ShiftAdd) error {
	return s.shiftRepository.Delete(shift)
}

func (s *shiftUseCase) UpdateHot(shiftHot []map[string]interface{}) error {
	return s.shiftRepository.UpdateHot(shiftHot)
}

// ExportToExcel exports shifts to Excel format
func (s *shiftUseCase) ExportToExcel(businessID int) (*excelize.File, error) {
	// Get shifts data
	shifts, err := s.shiftRepository.Fetch(businessID)
	if err != nil {
		return nil, fmt.Errorf("error fetching shift data: %v", err)
	}

	// Create new Excel file
	f := excelize.NewFile()
	sheetName := f.GetSheetName(0)

	// Define headers
	headers := []string{
		"Shift Code",
		"Shift Name",
		"Office",
		"Type",
		"Time In",
		"Time Out",
		"Tolerance",
		"Color",
		"Active",
	}

	// Add headers to the first row
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// Add data rows
	for i, shift := range shifts {
		row := i + 2 // Start from row 2 (after headers)
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), shift.ShiftCode)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), shift.Name)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), shift.ShiftOffice)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), shift.ShiftType)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), shift.ShiftIn)  //time in
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), shift.ShiftOut) //time out
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), shift.ShiftTolerance)
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), shift.ShiftColor)
		log.Info("shift data: %v", cast.ToString(shift))

		// Convert active status to yes/no
		activeStatus := "no"
		if shift.ShiftActive == 1 {
			activeStatus = "yes"
		}
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", row), activeStatus)

		// Add color fill if color code exists
		if shift.ShiftColor != "" {
			style, err := f.NewStyle(&excelize.Style{
				Fill: excelize.Fill{
					Type:    "pattern",
					Color:   []string{shift.ShiftColor},
					Pattern: 1,
				},
			})
			if err == nil {
				f.SetCellStyle(sheetName, fmt.Sprintf("H%d", row), fmt.Sprintf("H%d", row), style)
			}
		}
	}

	// Add styling to the header row
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#DDEBF7"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		log.Info("Error creating style: %v", err)
	} else {
		// Apply style to header row
		f.SetCellStyle(sheetName, "A1", "I1", style)
	}

	// Add column width for better readability
	f.SetColWidth(sheetName, "A", "I", 15)

	return f, nil
}

func (s *shiftUseCase) GetShiftNamesForDropdown(adminID int) ([]string, error) {
	return s.shiftRepository.GetShiftNamesForDropdown(adminID)
}

func (s *shiftUseCase) GetTypeNamesForDropdown(adminID int) ([]string, error) {
	return s.shiftRepository.GetTypeNamesForDropdown(adminID)
}
