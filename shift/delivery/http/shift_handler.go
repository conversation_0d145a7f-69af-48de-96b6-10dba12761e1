package http

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/gookit/validate"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"gitlab.com/backend/api-hrm/domain"
)

// ShiftHandler handler
type ShiftHandler struct {
	ShiftUseCase domain.ShiftUseCase
}

// NewShiftHandler func handler
// @Summary Initialize shift routes
// @Description Set up shift management endpoints
func NewShiftHandler(app *fiber.App, uc domain.ShiftUseCase) {
	handler := &ShiftHandler{ShiftUseCase: uc}
	v1 := app.Group("/v1")
	v1.Get("/shifts", handler.Fetch)
	v1.Post("/shifts/group", handler.FetchShiftGroup)
	v1.Post("/shifts/add", handler.Add)
	v1.Post("/shifts/delete", handler.Delete)
	v1.Post("/shifts/update_hot", handler.UpdateHot)
	v1.Post("/shifts/import", handler.Import)
	v1.Get("/shifts/template", handler.DownloadTemplate)
	v1.Get("/shifts/export", handler.Export)
}

// Fetch get all shift handler
// @Summary Get all shifts
// @Description Retrieve all shifts for a business with optional filtering by outlet IDs. When outlet_ids parameter is provided, only shifts for those specific outlets will be returned. Multiple outlet IDs can be specified as comma-separated values.
// @Tags shift
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param business_id header string true "Business ID"
// @Param outlet_ids query string false "Comma-separated outlet IDs for filtering (e.g., 45,67,4). If not provided, all shifts for the business will be returned."
// @Success 200 {array} domain.MasterShift "List of shifts matching the filter criteria"
// @Failure 500 {object} object{message=string,error=error,status=int} "Internal server error"
// @Router /v1/shifts [get]
func (s *ShiftHandler) Fetch(c *fiber.Ctx) error {
	// adminID := c.Get("user_id")
	businessId := c.Get("business_id")
	businessID, _ := strconv.Atoi(businessId)

	// Parse filter parameters
	var filter domain.ShiftFilter

	// Parse outlet IDs from query parameter (comma-separated)
	outletIDsParam := c.Query("outlet_ids")
	if outletIDsParam != "" {
		outletIDsStr := strings.Split(outletIDsParam, ",")
		for _, idStr := range outletIDsStr {
			if id, err := strconv.Atoi(strings.TrimSpace(idStr)); err == nil {
				filter.OutletIDs = append(filter.OutletIDs, id)
			}
		}
	}

	// Use filtered method if any filters are provided, otherwise use original method
	var shifts []domain.MasterShift
	var err error

	if len(filter.OutletIDs) > 0 {
		shifts, err = s.ShiftUseCase.FetchWithFilter(businessID, filter)
	} else {
		shifts, err = s.ShiftUseCase.Fetch(businessID)
	}

	if err != nil {
		fmt.Println("error: ", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching shift",
			"error":   err,
			"status":  c.Response().StatusCode(),
		})
	}
	return c.JSON(shifts)
}

// FetchShiftGroup get shiftGroup
// @Summary Get shift groups
// @Description Retrieve shift groups by outlet IDs
// @Tags shift
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param input body object{outlet_id=[]interface{}} true "Outlet IDs"
// @Success 200 {array} domain.ShiftGroup "List of shift groups"
// @Failure 500 {object} object{message=string,error=error} "Internal server error"
// @Router /v1/shifts/group [post]
func (s *ShiftHandler) FetchShiftGroup(c *fiber.Ctx) error {
	type req struct {
		OutletFkid []any `json:"outlet_id"`
	}
	var body req
	err := c.BodyParser(&body)
	if err != nil {
		log.Info("error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error parsing request body",
			"error":   err,
		})
	}
	shiftGroup, err := s.ShiftUseCase.FetchShiftGroup(body.OutletFkid)
	log.Info("fetchErr: %v", err)
	if err != nil {
		log.Info("error FetchShiftGroup: %v ", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching shift group",
			"error":   err,
		})
	}
	return c.JSON(shiftGroup)
	// return c.JSON(body)
}

// Add func
// @Summary Add or update shift
// @Description Create a new shift or update existing one
// @Tags shift
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param input body object{shift_id=int,shift_office=[]string,shift_type=int,shift_code=string,shift_fkid=string,shift_in=string,shift_out=string,shift_tolerance=string,shift_color=string,shift_active=string} true "Shift details"
// @Success 200 {object} object{message=string,status=int} "Success response"
// @Failure 400 {object} object{message=map[string]string,status=int} "Validation error"
// @Failure 500 {object} object{message=string,status=int,error=error} "Internal server error"
// @Router /v1/shifts/add [post]
func (s *ShiftHandler) Add(c *fiber.Ctx) error {
	type req struct {
		ShiftID        int           `json:"shift_id"`
		ShiftOffice    []interface{} `json:"shift_office"`
		ShiftType      int           `json:"shift_type"`
		ShiftCode      string        `json:"shift_code"`
		ShiftFkid      string        `json:"shift_fkid"`
		ShiftIn        string        `json:"shift_in"`
		ShiftOut       string        `json:"shift_out"`
		ShiftTolerance string        `json:"shift_tolerance"`
		ShiftColor     string        `json:"shift_color"`
		ShiftActive    string        `json:"shift_active"`
	}

	type conv struct {
		ShiftID        int    `json:"shift_id"`
		ShiftOffice    string `json:"shift_office"`
		ShiftType      int    `json:"shift_type"`
		ShiftCode      string `json:"shift_code"`
		ShiftFkid      string `json:"shift_fkid"`
		ShiftIn        string `json:"shift_in"`
		ShiftOut       string `json:"shift_out"`
		ShiftTolerance string `json:"shift_tolerance"`
		ShiftColor     string `json:"shift_color"`
		ShiftActive    string `json:"shift_active"`
	}
	var body req
	var reqParams []map[string]interface{}
	c.BodyParser(&body)
	var inputForm map[string]interface{}
	if len(body.ShiftOffice) == 1 {
		inputForm = cast.StructToMap(conv{
			ShiftID:        body.ShiftID,
			ShiftOffice:    body.ShiftOffice[0].(string),
			ShiftType:      body.ShiftType,
			ShiftCode:      body.ShiftCode,
			ShiftFkid:      body.ShiftFkid,
			ShiftIn:        body.ShiftIn,
			ShiftOut:       body.ShiftOut,
			ShiftTolerance: body.ShiftTolerance,
			ShiftColor:     body.ShiftColor,
			ShiftActive:    body.ShiftActive,
		})
		reqParams = append(reqParams, inputForm)
	} else if len(body.ShiftOffice) > 1 {
		for _, v := range body.ShiftOffice {
			inputForm = cast.StructToMap(conv{
				ShiftID:        body.ShiftID,
				ShiftOffice:    v.(string),
				ShiftType:      body.ShiftType,
				ShiftCode:      body.ShiftCode,
				ShiftFkid:      body.ShiftFkid,
				ShiftIn:        body.ShiftIn,
				ShiftOut:       body.ShiftOut,
				ShiftTolerance: body.ShiftTolerance,
				ShiftColor:     body.ShiftColor,
				ShiftActive:    body.ShiftActive,
			})
			reqParams = append(reqParams, inputForm)
		}
	}

	v := validate.Map(inputForm)
	v.StringRule("shift_office", "required")
	v.StringRule("shift_code", "required")
	v.StringRule("shift_in", "required")
	v.StringRule("shift_out", "required")
	v.StringRule("shift_type", "required")
	v.StringRule("shift_fkid", "required")
	v.StringRule("shift_tolerance", "required")
	v.StringRule("shift_color", "required")
	v.StringRule("shift_active", "required")

	v.AddMessages(map[string]string{
		"shift_office.required":    "Office harus di isi",
		"shift_type.required":      "Type shift harus di isi",
		"shift_code.required":      "Shift kode harus di isi",
		"shift_fkid.required":      "Fkid harus di isi",
		"shift_in.required":        "Time In harus di isi",
		"shift_out.required":       "Time Out harus di isi",
		"shift_tolerance.required": "Toleransi harus di isi",
		"shift_color.required":     "Warna harus di isi",
		"shift_active.required":    "ACtive harus di isi",
	})

	if v.Validate() {
		if body.ShiftID != 0 {
			err := s.ShiftUseCase.Update(reqParams)
			if err != nil {
				return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
					"message": "Failed update shift",
					"status":  0,
					"error":   err,
				})
			}
			return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
				"message": "Update data success",
				"status":  1,
			})
		}
		err := s.ShiftUseCase.Add(reqParams)
		if err != nil {
			return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
				"message": "Failed insert new data",
				"status":  0,
				"error":   err,
			})
		}
		return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
			"message": "Saving data success",
			"status":  1,
		})
	}
	fmt.Println(v.Errors)
	// validate return errors json
	var errFields map[string]string
	if v.Errors.HasField("shift_office") {
		errFields = map[string]string{"inputDepartment[]": v.Errors.Field("shift_office")["required"]}
	} else if v.Errors.HasField("shift_code") {
		errFields = map[string]string{"inputShiftCode": v.Errors.Field("shift_code")["required"]}
	} else if v.Errors.HasField("shift_type") {
		errFields = map[string]string{"inputCasual": v.Errors.Field("shift_type")["required"]}
	} else if v.Errors.HasField("shift_in") {
		errFields = map[string]string{"inputShiftIn": v.Errors.Field("shift_in")["required"]}
	} else if v.Errors.HasField("shift_out") {
		errFields = map[string]string{"inputShiftOut": v.Errors.Field("shift_out")["required"]}
	} else if v.Errors.HasField("shift_out") {
		errFields = map[string]string{"inputTolerance": v.Errors.Field("shift_tolerance")["required"]}
	}

	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": errFields,
		"status":  0,
	})
}

// Delete shift handler
// @Summary Delete shift
// @Description Delete an existing shift
// @Tags shift
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param input body domain.ShiftAdd true "Shift to delete"
// @Success 200 {object} object{message=string,status=int,error=error} "Success response"
// @Failure 500 {object} object{message=string,status=int,error=error} "Internal server error"
// @Router /v1/shifts/delete [post]
func (s *ShiftHandler) Delete(c *fiber.Ctx) error {
	var body domain.ShiftAdd
	c.BodyParser(&body)
	err := s.ShiftUseCase.Delete(body)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "Delete data failed",
			"status":  0,
			"error":   err,
		})
	}
	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": "Delete success",
		"status":  1,
		"error":   err,
	})
}

// UpdateHot shift
// @Summary Bulk update shifts
// @Description Update multiple shifts at once
// @Tags shift
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param input body object{collection=[]map[string]interface{}} true "Collection of shifts to update"
// @Success 200 {object} object{message=string,status=int} "Success response"
// @Failure 500 {object} object{message=string,status=int,error=error} "Internal server error"
// @Router /v1/shifts/update_hot [post]
func (s *ShiftHandler) UpdateHot(c *fiber.Ctx) error {
	type mapShift struct {
		Collection []map[string]interface{} `json:"collection"`
	}
	var body mapShift
	c.BodyParser(&body)
	for _, i := range body.Collection {
		delete(i, "shift_name_id")
		delete(i, "shift_id_name")
		delete(i, "shift_type_id")
		delete(i, "shift_office_id")
		delete(i, "shift_type")
		delete(i, "shift_max_in")
		delete(i, "shift_office")
		delete(i, "shift_fkid")
	}

	err := s.ShiftUseCase.UpdateHot(body.Collection)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "Failed insert new data",
			"status":  0,
			"error":   err,
		})
	}
	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": "Saving data success",
		"status":  1,
	})
}

// Export shifts to Excel
// @Summary Export shifts to Excel
// @Description Export all shifts for a business to Excel file
// @Tags shift
// @Accept json
// @Produce application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
// @Security BearerAuth
// @Success 200 {file} file "Excel file with shift data"
// @Failure 500 {object} object{message=string,error=error,status=int} "Internal server error"
// @Router /v1/shifts/export [get]
func (s *ShiftHandler) Export(c *fiber.Ctx) error {
	businessId := c.Get("business_id")
	businessID, _ := strconv.Atoi(businessId)

	// Get Excel file from usecase
	f, err := s.ShiftUseCase.ExportToExcel(businessID)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error generating Excel file",
			"error":   err,
			"status":  c.Response().StatusCode(),
		})
	}

	// Set content type and headers for file download
	c.Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Set("Content-Disposition", "attachment; filename=shifts_export.xlsx")

	// Write the file to response
	buffer, err := f.WriteToBuffer()
	if err != nil {
		log.Info("Error writing Excel file to buffer: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "Error generating Excel file",
			"status":  0,
			"error":   err.Error(),
		})
	}

	return c.Send(buffer.Bytes())
}
