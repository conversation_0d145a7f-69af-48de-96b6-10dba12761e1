package http

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/xuri/excelize/v2"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/request"
	"gitlab.com/backend/api-hrm/domain"
)

const (
	shiftCodeHeader = "Shift Code"
	shiftNameHeader = "Shift Name"
	timeInHeader    = "Time In"
	timeOutHeader   = "Time Out"
	toleranceHeader = "Tolerance"
	typeHeader      = "Type"
	colorHeader     = "Color"
	activeHeader    = "Active"
)

var headers = []string{shiftCodeHeader, shiftNameHeader, timeInHeader, timeOutHeader, toleranceHeader, typeHeader, colorHeader, activeHeader}
var requiredHeaders = []string{shiftCodeHeader, shiftNameHeader, timeInHeader, timeOutHeader, toleranceHeader, typeHeader}

// parseShiftExcelFile parses the Excel file and returns a slice of ShiftImport
func parseShiftExcelFile(filePath string) ([]domain.ShiftImport, error) {
	// Open Excel file
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel file: %v", err)
	}
	defer f.Close()

	// Get the first sheet
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		return nil, fmt.Errorf("no sheets found in Excel file")
	}

	// Get all rows
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("failed to get rows: %v", err)
	}

	// Check if there are enough rows (at least header + 1 data row)
	if len(rows) < 2 {
		return nil, fmt.Errorf("excel file must contain at least a header row and one data row")
	}

	// Validate header row - first 6 columns are required, last 2 are optional

	// Optional headers: "Color", "Active" - handled in the row processing

	// Check if required headers are present
	// if len(rows[0]) < len(requiredHeaders) {
	// 	return nil, fmt.Errorf("Excel file must contain columns for: %s", strings.Join(requiredHeaders, ", "))
	// }

	//search the first row

	// Process data rows
	var shifts []domain.ShiftImport
	var headerRow []string
	var columnMap map[string]int

	// Find header row and create column mapping
	for _, row := range rows {
		if len(row) < len(requiredHeaders) {
			continue
		}

		// Check if this is the header row
		if strings.EqualFold(row[0], requiredHeaders[0]) {
			headerRow = row
			columnMap = make(map[string]int)
			for j, header := range row {
				columnMap[strings.ToLower(header)] = j
			}
			break
		}
	}

	// Validate all required headers are present
	if headerRow == nil {
		return nil, fmt.Errorf("excel file must contain columns for: %s", strings.Join(requiredHeaders, ", "))
	}

	for _, header := range requiredHeaders {
		if _, exists := columnMap[strings.ToLower(header)]; !exists {
			return nil, fmt.Errorf("excel file must contain columns for: %s", strings.Join(requiredHeaders, ", "))
		}
	}

	// Process data rows
	for _, row := range rows {
		// Skip header row and empty rows
		if len(row) < len(requiredHeaders) || row[0] == "" {
			continue
		}

		// Skip if this is another header row
		if strings.EqualFold(row[0], requiredHeaders[0]) {
			continue
		}

		// Create shift import data using column mapping
		shift := domain.ShiftImport{
			ShiftCode:      row[columnMap["shift code"]],
			ShiftName:      row[columnMap["shift name"]],
			TimeIn:         row[columnMap["time in"]],
			TimeOut:        row[columnMap["time out"]],
			ShiftTolerance: row[columnMap["tolerance"]],
			TypeName:       row[columnMap["type"]],
		}

		// Add optional fields if they exist
		if colorIdx, exists := columnMap["color"]; exists && len(row) > colorIdx && row[colorIdx] != "" {
			shift.ShiftColor = row[colorIdx]
		}

		if activeIdx, exists := columnMap["active"]; exists && len(row) > activeIdx && row[activeIdx] != "" {
			shift.ShiftActive = row[activeIdx]
		}

		shifts = append(shifts, shift)
	}

	if len(shifts) == 0 {
		return nil, fmt.Errorf("no valid shift data found in Excel file")
	}

	return shifts, nil
}

// DownloadTemplate generates and serves an Excel template for shift import
// @Summary Download Excel template for shift import
// @Description Download an Excel template file with the correct format for importing shifts
// @Tags shift
// @Accept json
// @Produce application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
// @Security BearerAuth
// @Success 200 {file} file "Excel template file"
// @Failure 500 {object} object{message=string,status=int,error=error} "Internal server error"
// @Router /v1/shifts/template [get]
func (s *ShiftHandler) DownloadTemplate(c *fiber.Ctx) error {
	// Get business ID from context
	businessId := c.Get("business_id")
	businessID, _ := strconv.Atoi(businessId)

	// Get existing shift names and types for dropdowns
	shiftNames, err := s.ShiftUseCase.GetShiftNamesForDropdown(businessID)
	if err != nil {
		log.Info("Error fetching shift names: %v", err)
		shiftNames = []string{} // Continue with empty list if error
	}

	typeNames, err := s.ShiftUseCase.GetTypeNamesForDropdown(businessID)
	if err != nil {
		log.Info("Error fetching type names: %v", err)
		typeNames = []string{} // Continue with empty list if error
	}

	// Create a new Excel file
	f := excelize.NewFile()
	sheetName := f.GetSheetName(0)

	// Add headers to the first row
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// Add example data for two rows
	exampleData := [][]string{
		{
			"MORNING",
			"Morning Shift",
			"08:00",
			"17:00",
			"15",
			"FULL TIME",
			"#FF5733",
			"yes",
		},
		{
			"NIGHT",
			"Night Shift",
			"22:00",
			"06:00",
			"10",
			"PART TIME",
			"#3366FF",
			"no",
		},
	}

	// Add example data to rows 2 and 3
	for rowIdx, rowData := range exampleData {
		for colIdx, value := range rowData {
			cell, _ := excelize.CoordinatesToCellName(colIdx+1, rowIdx+2)
			f.SetCellValue(sheetName, cell, value)
		}
	}

	// Add dropdown for Shift Name column (column B)
	if len(shiftNames) > 0 {
		dv := excelize.NewDataValidation(false)
		dv.Sqref = "B2:B1000" // Apply to all rows
		dv.SetDropList(shiftNames)
		dv.ShowErrorMessage = false
		dv.ShowInputMessage = true
		dv.PromptTitle = &[]string{"Shift Name"}[0]
		dv.Prompt = &[]string{"Choose from existing shifts or type a new one"}[0]
		dv.ErrorTitle = &[]string{"Invalid Shift Name"}[0]
		dv.Error = &[]string{"Please choose from the list or enter a valid shift name"}[0]
		if err := f.AddDataValidation(sheetName, dv); err != nil {
			log.Info("Error adding shift name validation: %v", err)
		}
	}

	// Add dropdown for Type column (column F)
	if len(typeNames) > 0 {
		dv := excelize.NewDataValidation(false)
		dv.Sqref = "F2:F1000" // Apply to all rows
		dv.SetDropList(typeNames)
		dv.ShowErrorMessage = false
		dv.ShowInputMessage = true
		dv.PromptTitle = &[]string{"Type"}[0]
		dv.Prompt = &[]string{"Choose from existing types or type a new one"}[0]
		dv.ErrorTitle = &[]string{"Invalid Type"}[0]
		dv.Error = &[]string{"Please choose from the list or enter a valid type"}[0]
		if err := f.AddDataValidation(sheetName, dv); err != nil {
			log.Info("Error adding type validation: %v", err)
		}
	}

	//add dropdown for active
	dv := excelize.NewDataValidation(true)
	dv.Sqref = "H2:H1000" // Apply to all rows
	dv.SetDropList([]string{"yes", "no"})
	dv.ShowErrorMessage = true
	dv.ShowInputMessage = true
	dv.PromptTitle = &[]string{"Active"}[0]
	dv.Prompt = &[]string{"The value must be yes or no"}[0]
	dv.ErrorTitle = &[]string{"Invalid Active"}[0]
	dv.Error = &[]string{"Please choose from the list or enter a valid active"}[0]
	dv.AllowBlank = true
	if err := f.AddDataValidation(sheetName, dv); err != nil {
		log.Info("Error adding active validation: %v", err)
	}

	// Add styling to the header row
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#DDEBF7"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		log.Info("Error creating style: %v", err)
	} else {
		// Apply style to header row
		f.SetCellStyle(sheetName, "A1", "H1", style)
	}

	// Add column width for better readability
	f.SetColWidth(sheetName, "A", "H", 15)

	// Add notes/instructions in a separate sheet
	f.NewSheet("Instructions")
	f.SetCellValue("Instructions", "A1", "Instructions for Shift Import Template")
	f.SetCellValue("Instructions", "A3", "Required Columns:")
	f.SetCellValue("Instructions", "A4", "1. Shift Code - Unique code for the shift")
	f.SetCellValue("Instructions", "A5", "2. Shift Name - Must exist in the shift table (dropdown available)")
	f.SetCellValue("Instructions", "A6", "3. Time In - Format: HH:MM or HH:MM:SS")
	f.SetCellValue("Instructions", "A7", "4. Time Out - Format: HH:MM or HH:MM:SS")
	f.SetCellValue("Instructions", "A8", "5. Tolerance - Number of minutes tolerance for check-in")
	f.SetCellValue("Instructions", "A9", "6. Type - Must exist in the employee type table (dropdown available)")

	f.SetCellValue("Instructions", "A11", "Optional Columns:")
	f.SetCellValue("Instructions", "A12", "7. Color - Must start with # (e.g., #FF5733), defaults to white (#FFFFFF) if blank")
	f.SetCellValue("Instructions", "A13", "8. Active - 'yes' or 'no', defaults to 'yes' if blank")

	// Set content type and headers for file download
	c.Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Set("Content-Disposition", "attachment; filename=shift_import_template.xlsx")

	// Write the file to response
	buffer, err := f.WriteToBuffer()
	if err != nil {
		log.Info("Error writing Excel file to buffer: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "Error generating Excel template",
			"status":  0,
			"error":   err.Error(),
		})
	}

	return c.Send(buffer.Bytes())
}

// Import shifts from Excel file
// @Summary Import shifts from Excel file
// @Description Import shifts from Excel file with outlet IDs
// @Tags shift
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param outlet_ids formData string true "Comma-separated outlet IDs"
// @Param file formData file true "Excel file with shift data"
// @Success 200 {object} object{message=string,status=int} "Success response"
// @Failure 400 {object} object{message=string,status=int} "Bad request"
// @Failure 500 {object} object{message=string,status=int,error=error} "Internal server error"
// @Router /v1/shifts/import [post]
func (s *ShiftHandler) Import(c *fiber.Ctx) error {
	// Get business ID from context
	businessId := c.Get("business_id")
	businessID, _ := strconv.Atoi(businessId)

	// Parse form
	form, err := c.MultipartForm()
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "Invalid form data",
			"status":  0,
			"error":   err.Error(),
		})
	}

	// Get outlet IDs
	outletIDsStr := form.Value["outlet_ids"]
	if len(outletIDsStr) == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "Outlet IDs are required",
			"status":  0,
		})
	}

	// Parse outlet IDs
	outletIDsStrArray := strings.Split(outletIDsStr[0], ",")
	var outletIDs []int
	for _, idStr := range outletIDsStrArray {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
				"message": fmt.Sprintf("Invalid outlet ID: %s", idStr),
				"status":  0,
			})
		}
		outletIDs = append(outletIDs, id)
	}

	// Get file
	files := form.File["file"]
	if len(files) == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "Excel file is required",
			"status":  0,
		})
	}

	// Save file to temp directory
	file := files[0]
	tempFilePath, err := request.SaveToTempDir(file)
	if err != nil {
		log.Info("Error saving file: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "Error saving file",
			"status":  0,
			"error":   err.Error(),
		})
	}
	defer os.RemoveAll(filepath.Dir(tempFilePath)) // Clean up temp directory

	// Parse Excel file
	shifts, err := parseShiftExcelFile(tempFilePath)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "Error parsing Excel file",
			"status":  0,
			"error":   err.Error(),
		})
	}

	// Import shifts
	err = s.ShiftUseCase.ImportShifts(outletIDs, shifts, businessID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "Error importing shifts",
			"status":  0,
			"error":   err.Error(),
		})
	}

	return c.Status(fiber.StatusOK).JSON(&fiber.Map{
		"message": fmt.Sprintf("Successfully imported %d shifts", len(shifts)),
		"status":  1,
	})
}
