package http

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/request"
	"gitlab.com/backend/api-hrm/domain"
)

// PresensiHandler struct
type PresensiHandler struct {
	PresensiUseCase domain.PresensiUseCase
}

// NewPresensiHandler func
// @Summary Initialize presensi routes
// @Description Set up presensi related endpoints
func NewPresensiHandler(app *fiber.App, uc domain.PresensiUseCase) {
	handler := &PresensiHandler{PresensiUseCase: uc}
	v1 := app.Group("/v1")
	v1.Get("/presensi", handler.Fetch)
	v1.Post("/presensi/import", handler.Import)
	v1.Get("/presensi/template", handler.DownloadTemplate)
}

// Fetch get all presensi
// @Summary Get all attendance records
// @Description Retrieve all employee attendance records including late status, overtime, and break times
// @Tags presensi
// @Accept json
// @Produce json
// @Success 200 {array} domain.Presensi "List of attendance records"
// @Failure 500 {object} error "Internal server error"
// @Router /v1/presensi [get]
func (p *PresensiHandler) Fetch(c *fiber.Ctx) error {
	presensi, err := p.PresensiUseCase.Fetch()
	if err != nil {
		fmt.Println("error: ", err)
		return nil
	}
	return c.JSON(presensi)
}

// Import presensi from Excel file
// @Summary Import presensi from Excel file
// @Description Import presensi data from Excel file with outlet ID and overwrite option
// @Tags presensi
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param outlet_id formData string true "Outlet ID"
// @Param overwrite formData string false "Overwrite existing data (true/false)"
// @Param file formData file true "Excel file with presensi data"
// @Success 200 {object} object{message=string,status=int} "Success response"
// @Failure 400 {object} object{message=string,status=int} "Bad request"
// @Failure 500 {object} object{message=string,status=int,error=error} "Internal server error"
// @Router /v1/presensi/import [post]
func (p *PresensiHandler) Import(c *fiber.Ctx) error {
	// Parse multipart form
	form, err := c.MultipartForm()
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "Error parsing form data",
			"status":  0,
		})
	}

	// Get outlet ID
	outletIDStr := form.Value["outlet_id"]
	if len(outletIDStr) == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "outlet_id is required",
			"status":  0,
		})
	}

	outletID, err := strconv.Atoi(outletIDStr[0])
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "Invalid outlet_id format",
			"status":  0,
		})
	}

	// Get overwrite parameter (optional, defaults to false)
	overwrite := false
	if overwriteStr := form.Value["overwrite"]; len(overwriteStr) > 0 {
		overwrite, _ = strconv.ParseBool(overwriteStr[0])
	}

	// Get file
	files := form.File["file"]
	if len(files) == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "Excel file is required",
			"status":  0,
		})
	}

	// Save file to temp directory
	file := files[0]
	tempFilePath, err := request.SaveToTempDir(file)
	if err != nil {
		log.Info("Error saving file: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "Error saving file",
			"status":  0,
			"error":   err.Error(),
		})
	}
	defer os.RemoveAll(filepath.Dir(tempFilePath)) // Clean up temp directory

	// Call usecase with file path and parameters
	err = p.PresensiUseCase.ImportPresensi(outletID, overwrite, tempFilePath)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
			"message": "Error importing presensi data",
			"status":  0,
			"error":   err.Error(),
		})
	}

	return c.JSON(&fiber.Map{
		"message": "Presensi data imported successfully",
		"status":  1,
	})
}

// DownloadTemplate generates and serves an Excel template for presensi import
// @Summary Download Excel template for presensi import
// @Description Download an Excel template file with the correct format for importing attendance data
// @Tags presensi
// @Accept json
// @Produce application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
// @Security BearerAuth
// @Success 200 {file} file "Excel template file"
// @Failure 500 {object} object{message=string,status=int,error=error} "Internal server error"
// @Router /v1/presensi/template [get]
func (p *PresensiHandler) DownloadTemplate(c *fiber.Ctx) error {
	// Generate the Excel template using the usecase
	excelFile, err := p.PresensiUseCase.GenerateImportTemplate()
	if err != nil {
		log.Info("Error generating presensi template: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "Error generating Excel template",
			"status":  0,
			"error":   err.Error(),
		})
	}

	// Set content type and headers for file download
	c.Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Set("Content-Disposition", "attachment; filename=presensi_import_template.xlsx")

	// Write the file to response
	buffer, err := excelFile.WriteToBuffer()
	if err != nil {
		log.Info("Error writing Excel file to buffer: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(&fiber.Map{
			"message": "Error generating Excel template",
			"status":  0,
			"error":   err.Error(),
		})
	}

	return c.Send(buffer.Bytes())
}
