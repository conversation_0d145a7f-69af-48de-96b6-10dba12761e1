package http

import (
	"net/http/httptest"
	"testing"

	"github.com/gofiber/fiber/v2"
	"github.com/xuri/excelize/v2"
	"gitlab.com/backend/api-hrm/domain"
)

// MockPresensiUseCase for testing
type MockPresensiUseCase struct{}

func (m *MockPresensiUseCase) Fetch() ([]domain.Presensi, error) {
	return []domain.Presensi{}, nil
}

func (m *MockPresensiUseCase) ImportPresensi(outletID int, overwrite bool, filePath string) error {
	return nil
}

func (m *MockPresensiUseCase) GenerateImportTemplate() (*excelize.File, error) {
	// Create a simple Excel file for testing
	f := excelize.NewFile()
	f.SetCellValue("Sheet1", "A1", "Test Template")
	return f, nil
}

func TestDownloadTemplate(t *testing.T) {
	// Create Fiber app
	app := fiber.New()

	// Create mock usecase
	mockUseCase := &MockPresensiUseCase{}

	// Create handler
	handler := &PresensiHandler{PresensiUseCase: mockUseCase}

	// Setup route
	app.Get("/template", handler.DownloadTemplate)

	// Create test request
	req := httptest.NewRequest("GET", "/template", nil)

	// Perform request
	resp, err := app.Test(req)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	// Check response status
	if resp.StatusCode != 200 {
		t.Fatalf("Expected status 200, got: %d", resp.StatusCode)
	}

	// Check content type
	contentType := resp.Header.Get("Content-Type")
	expectedContentType := "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	if contentType != expectedContentType {
		t.Fatalf("Expected content type %s, got: %s", expectedContentType, contentType)
	}

	// Check content disposition
	contentDisposition := resp.Header.Get("Content-Disposition")
	expectedDisposition := "attachment; filename=presensi_import_template.xlsx"
	if contentDisposition != expectedDisposition {
		t.Fatalf("Expected content disposition %s, got: %s", expectedDisposition, contentDisposition)
	}

	t.Log("Template download endpoint test passed successfully")
}
