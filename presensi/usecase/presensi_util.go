package usecase

import (
	"fmt"
	"strings"
	"time"
)

// formatDate handles different date formats and converts them to YYYY-MM-DD format
func formatDate(dateStr string) (string, error) {
	// Handle different date formats as in the original PHP code
	if len(dateStr) >= 5 && dateStr[4:5] == "/" {
		// Format: YYYY/MM/DD
		parts := strings.Split(dateStr, "/")
		return strings.Join(parts, "-"), nil
	} else if len(dateStr) >= 5 && dateStr[4:5] == "-" {
		// Format: YYYY-MM-DD
		return dateStr, nil
	} else if len(dateStr) >= 3 && dateStr[2:3] == "/" {
		// Format: DD/MM/YYYY
		parts := strings.Split(dateStr, "/")
		if len(parts) == 3 {
			return fmt.Sprintf("%s-%s-%s", parts[2], parts[1], parts[0]), nil
		}
	} else if len(dateStr) >= 3 && dateStr[2:3] == "-" {
		// Format: DD-MM-YYYY
		parts := strings.Split(dateStr, "-")
		if len(parts) == 3 {
			// Try to parse as DD-MM-YYYY first
			if _, err := time.Parse("02-01-2006", dateStr); err == nil {
				return fmt.Sprintf("%s-%s-%s", parts[2], parts[1], parts[0]), nil
			}
			// Try MM-DD-YYYY
			return fmt.Sprintf("%s-%s-%s", parts[2], parts[0], parts[1]), nil
		}
	}

	return dateStr, nil
}
