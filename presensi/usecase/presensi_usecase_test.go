package usecase

import (
	"testing"

	"gitlab.com/backend/api-hrm/domain"
)

// MockPresensiRepository for testing
type MockPresensiRepository struct{}

func (m *MockPresensiRepository) Fetch() ([]domain.Presensi, error) {
	return []domain.Presensi{}, nil
}

func (m *MockPresensiRepository) ValidateEmployeeNIKs(niks []string) ([]domain.EmployeeNIK, error) {
	return []domain.EmployeeNIK{}, nil
}

func (m *MockPresensiRepository) CheckExistingHashes(hashes []string) ([]string, error) {
	return []string{}, nil
}

func (m *MockPresensiRepository) InsertPresensiData(data []domain.PresensiTransformed, overwrite bool) error {
	return nil
}

func TestGenerateImportTemplate(t *testing.T) {
	// Create usecase with mock repository
	mockRepo := &MockPresensiRepository{}
	usecase := NewPresensiUseCase(mockRepo)

	// Test template generation
	excelFile, err := usecase.GenerateImportTemplate()
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	if excelFile == nil {
		t.Fatal("Expected Excel file to be generated, got nil")
	}

	// Test that we can write the file to buffer (basic functionality test)
	buffer, err := excelFile.WriteToBuffer()
	if err != nil {
		t.Fatalf("Expected no error writing to buffer, got: %v", err)
	}

	if len(buffer.Bytes()) == 0 {
		t.Fatal("Expected non-empty buffer, got empty buffer")
	}

	t.Logf("Successfully generated Excel template with %d bytes", len(buffer.Bytes()))
}
