package http

import (
	"testing"

	"gitlab.com/backend/api-hrm/domain"
)

func TestEmployeeDataEmailField(t *testing.T) {
	// Test that EmployeeData struct includes email field
	employeeData := domain.EmployeeData{
		Name:    "<PERSON>",
		Email:   "<EMAIL>",
		Phone:   "**********",
		Address: "123 Main St",
	}

	// Test ToEmployeeMap includes email
	employeeMap := employeeData.ToEmployeeMap(0)

	if employeeMap["email"] != "<EMAIL>" {
		t.Errorf("Expected email to be '<EMAIL>', got %v", employeeMap["email"])
	}

	if employeeMap["name"] != "<PERSON>" {
		t.<PERSON>("Expected name to be '<PERSON>', got %v", employeeMap["name"])
	}

	if employeeMap["phone"] != "**********" {
		t.<PERSON><PERSON><PERSON>("Expected phone to be '**********', got %v", employeeMap["phone"])
	}

	if employeeMap["address"] != "123 Main St" {
		t.<PERSON><PERSON><PERSON>("Expected address to be '123 Main St', got %v", employeeMap["address"])
	}
}

func TestEmployeeFormStructure(t *testing.T) {
	// Test that the EmployeeForm struct can be created with email field
	// This is a compile-time test to ensure the struct has the email field

	// Create a mock form to test the structure
	type TestEmployeeForm struct {
		Name    string `form:"name"`
		Email   string `form:"email"`
		Phone   string `form:"phone"`
		Address string `form:"address"`
	}

	form := TestEmployeeForm{
		Name:    "Jane Doe",
		Email:   "<EMAIL>",
		Phone:   "**********",
		Address: "456 Oak Ave",
	}

	if form.Email != "<EMAIL>" {
		t.Errorf("Expected email to be '<EMAIL>', got %v", form.Email)
	}
}

func TestAccountsTableIntegration(t *testing.T) {
	// Test that the accounts table integration is properly structured
	// This test verifies the data structure that would be inserted into accounts table

	accountData := map[string]interface{}{
		"name":       "Test Employee",
		"email":      "<EMAIL>",
		"phone":      "**********",
		"password":   "hashedpassword123",
		"created_at": int64(**********), // Example timestamp
		"updated_at": int64(**********),
		"is_active":  1,
	}

	// Verify all required fields are present
	if accountData["name"] != "Test Employee" {
		t.Errorf("Expected name to be 'Test Employee', got %v", accountData["name"])
	}

	if accountData["email"] != "<EMAIL>" {
		t.Errorf("Expected email to be '<EMAIL>', got %v", accountData["email"])
	}

	if accountData["phone"] != "**********" {
		t.Errorf("Expected phone to be '**********', got %v", accountData["phone"])
	}

	if accountData["is_active"] != 1 {
		t.Errorf("Expected is_active to be 1, got %v", accountData["is_active"])
	}

	// Verify password field exists (should be hashed)
	if accountData["password"] == "" {
		t.Error("Password field should not be empty")
	}
}
