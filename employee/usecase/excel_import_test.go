package usecase

import (
	"testing"
	"time"

	"gitlab.com/backend/api-hrm/domain"
)

// Mock repository for testing Excel import
type mockExcelImportRepository struct {
	employees     map[string]*domain.Employee
	employeeTypes map[string]*domain.HrmMasterType
	jabatans      map[string]*domain.EmployeesJabatan
	outlets       map[string]*domain.Outlet
	accounts      map[string]*domain.Account
	nextAccountID int64
}

func newMockExcelImportRepository() *mockExcelImportRepository {
	return &mockExcelImportRepository{
		employees:     make(map[string]*domain.Employee),
		employeeTypes: make(map[string]*domain.HrmMasterType),
		jabatans:      make(map[string]*domain.EmployeesJabatan),
		outlets:       make(map[string]*domain.Outlet),
		accounts:      make(map[string]*domain.Account),
		nextAccountID: 1,
	}
}

// Implement required methods for Excel import testing
func (m *mockExcelImportRepository) FindEmployeeByNIK(nik string, adminID int) (*domain.Employee, error) {
	key := nik + "_" + string(rune(adminID))
	if emp, exists := m.employees[key]; exists {
		return emp, nil
	}
	return nil, nil
}

func (m *mockExcelImportRepository) FindEmployeeTypeByName(typeName string, adminID int) (*domain.HrmMasterType, error) {
	if empType, exists := m.employeeTypes[typeName]; exists {
		return empType, nil
	}
	return nil, nil
}

func (m *mockExcelImportRepository) FindJabatanByName(jabatanName string) (*domain.EmployeesJabatan, error) {
	if jabatan, exists := m.jabatans[jabatanName]; exists {
		return jabatan, nil
	}
	return nil, nil
}

func (m *mockExcelImportRepository) FindOutletByName(outletName string, adminID int) (*domain.Outlet, error) {
	if outlet, exists := m.outlets[outletName]; exists {
		return outlet, nil
	}
	return nil, nil
}

func (m *mockExcelImportRepository) FindAccountByEmail(email string) (*domain.Account, error) {
	if account, exists := m.accounts[email]; exists {
		return account, nil
	}
	return nil, nil
}

func (m *mockExcelImportRepository) CreateAccount(account domain.Account) (int64, error) {
	account.ID = m.nextAccountID
	m.nextAccountID++
	m.accounts[account.Email] = &account
	return account.ID, nil
}

func (m *mockExcelImportRepository) UpsertEmployee(employee domain.EmployeeData, isUpdate bool) (int64, error) {
	// Mock implementation - just return a fake ID
	return 123, nil
}

func (m *mockExcelImportRepository) DeleteAdditionalInfo(hrmEmployeeID int) error {
	return nil
}

func (m *mockExcelImportRepository) AddInfo(addInfo []map[string]any) error {
	return nil
}

// Add other required methods with empty implementations
func (m *mockExcelImportRepository) Fetch(adminFkid int) ([]domain.Employee, error) {
	return []domain.Employee{}, nil
}

func (m *mockExcelImportRepository) FetchSingle(hrmID int, employeeID int) (domain.EmployeeDetail, error) {
	return domain.EmployeeDetail{}, nil
}

func (m *mockExcelImportRepository) AddEmployee(employee domain.EmployeeData) (int64, error) {
	return 0, nil
}

func (m *mockExcelImportRepository) GetAddInfo(empID int) ([]domain.HrmAddInfo, error) {
	return []domain.HrmAddInfo{}, nil
}

func (m *mockExcelImportRepository) UpdateInfo(updateInfo []map[string]any) error {
	return nil
}

func (m *mockExcelImportRepository) DeleteAttach(where map[string]any) error {
	return nil
}

func (m *mockExcelImportRepository) FetchEmpAttach(addID int) ([]domain.EmployeeAttach, error) {
	return []domain.EmployeeAttach{}, nil
}

func (m *mockExcelImportRepository) FetchEmpImg(empID int) (domain.ProfileImage, error) {
	return domain.ProfileImage{}, nil
}

func (m *mockExcelImportRepository) GetImageVector(file1, file2 interface{}, fileName string, empID string) error {
	return nil
}

func (m *mockExcelImportRepository) FetchVectorImg(empID int) ([]domain.VectorImg, error) {
	return []domain.VectorImg{}, nil
}

func (m *mockExcelImportRepository) UpdatePofilePhoto(empID int, img string, vector interface{}) error {
	return nil
}

func (m *mockExcelImportRepository) ChangePassword(email string, nePass string, oldPass string) error {
	return nil
}

func (m *mockExcelImportRepository) FetchEmailNPassword(email string) (domain.EmailNPassword, error) {
	return domain.EmailNPassword{}, nil
}

func (m *mockExcelImportRepository) InsertUserKey(email string, BcryptStr string) error {
	return nil
}

func (m *mockExcelImportRepository) FetchEmployeeOutlet(empID int) ([]domain.EmployeeOutlet, error) {
	return []domain.EmployeeOutlet{}, nil
}

func (m *mockExcelImportRepository) FetchAddInfo(hrmIDs ...int) ([]domain.HrmAddInfo, error) {
	return []domain.HrmAddInfo{}, nil
}

func (m *mockExcelImportRepository) FetchWithFilter(adminFkid int, filter domain.EmployeeFilter) ([]domain.Employee, error) {
	return []domain.Employee{}, nil
}

// Test helper functions
func setupMockData(repo *mockExcelImportRepository) {
	// Add test employee types
	repo.employeeTypes["Casual 4"] = &domain.HrmMasterType{
		TypeID:   1,
		TypeName: "Casual 4",
	}

	// Add test jabatans
	repo.jabatans["Staff"] = &domain.EmployeesJabatan{
		JabatanID: 1,
		Name:      "Staff",
	}

	// Add test outlets
	repo.outlets["Main Office"] = &domain.Outlet{
		OutletID: 1,
		Name:     "Main Office",
	}
}

func TestProcessEmployeeData(t *testing.T) {
	// Setup mock repository
	mockRepo := newMockExcelImportRepository()
	setupMockData(mockRepo)

	// Create usecase with mock repository
	usecase := &employeeUseCase{
		employeeRepository: mockRepo,
	}

	// Test data
	excelEmployee := domain.ExcelEmployeeData{
		Row:         2,
		NIK:         "1234567890123456",
		Name:        "John Doe",
		TypeName:    "Casual 4",
		Email:       "<EMAIL>",
		Phone:       "081234567890",
		JabatanName: "Staff",
		DateJoin:    "2023/01/15",
		OutletName:  "Main Office",
		MaxLeave:    12,
		Salary:      "5.000.000",
		NPWP:        "123456789012345",
		Address:     "Jakarta",
		AdditionalInfo: map[string]string{
			"GOLONGAN DARAH": "A",
			"TINGGI BADAN":   "170",
		},
	}

	// Test processing
	processed, err := usecase.processEmployeeData(excelEmployee, 1)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	// Verify processed data
	if processed.TypeID != 1 {
		t.Errorf("Expected TypeID 1, got %d", processed.TypeID)
	}

	if processed.JabatanID != 1 {
		t.Errorf("Expected JabatanID 1, got %d", processed.JabatanID)
	}

	if processed.OutletID != 1 {
		t.Errorf("Expected OutletID 1, got %d", processed.OutletID)
	}

	if processed.SalaryInt != 5000000 {
		t.Errorf("Expected SalaryInt 5000000, got %d", processed.SalaryInt)
	}

	if processed.IsNewEmployee != true {
		t.Errorf("Expected IsNewEmployee true, got %v", processed.IsNewEmployee)
	}

	if processed.IsNewAccount != true {
		t.Errorf("Expected IsNewAccount true, got %v", processed.IsNewAccount)
	}
}

func TestParseSalary(t *testing.T) {
	tests := []struct {
		input    string
		expected int
		hasError bool
	}{
		{"5.000.000", 5000000, false},
		{"1.500.000", 1500000, false},
		{"500000", 500000, false},
		{"invalid", 0, true},
	}

	for _, test := range tests {
		result, err := parseSalary(test.input)
		if test.hasError {
			if err == nil {
				t.Errorf("Expected error for input %s, got none", test.input)
			}
		} else {
			if err != nil {
				t.Errorf("Expected no error for input %s, got: %v", test.input, err)
			}
			if result != test.expected {
				t.Errorf("Expected %d for input %s, got %d", test.expected, test.input, result)
			}
		}
	}
}

func TestParseDateToUnix(t *testing.T) {
	result, err := parseDateToUnix("2023/01/15")
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	// Verify it's a reasonable timestamp (in milliseconds)
	if result <= 0 {
		t.Errorf("Expected positive timestamp, got %d", result)
	}

	// Convert back to verify
	expectedTime := time.Date(2023, 1, 15, 0, 0, 0, 0, time.UTC)
	if result != expectedTime.Unix()*1000 {
		t.Errorf("Expected %d, got %d", expectedTime.Unix()*1000, result)
	}
}
