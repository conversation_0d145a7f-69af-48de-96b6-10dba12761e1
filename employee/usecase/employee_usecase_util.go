package usecase

import (
	"fmt"
	"os"
	"time"

	"gitlab.com/backend/api-hrm/core/util/bucket"
)

func (e *employeeUseCase) uploadAttachment(attachment string, employeeFkid string) (string, error) {
	file, err := os.Open(attachment)
	if err != nil {
		return "", err
	}
	defer file.Close()
	//if employeeId is empty, e.g when creating new user (has no yet id)
	if employeeFkid == "" {
		employeeFkid = "0" //use 0 as default
	}
	url, err := bucket.UploadBucket(file, attachment, employeeFkid, true)
	if err != nil {
		return "", err
	}
	return url, nil
}

func FormatTenure(joinTime int64) string {
	var tenure string = ""

	if joinTime > 0 {
		// Convert milliseconds to time
		joinTime := time.Unix(joinTime/1000, 0)
		now := time.Now()

		// Calculate the difference
		years := now.Year() - joinTime.Year()
		months := int(now.Month()) - int(joinTime.Month())
		days := now.Day() - joinTime.Day()

		// Adjust for negative days
		if days < 0 {
			months--
			// Get the last day of the previous month
			lastMonth := now.AddDate(0, -1, 0)
			days += time.Date(lastMonth.Year(), lastMonth.Month()+1, 0, 0, 0, 0, 0, time.UTC).Day()
		}

		// Adjust for negative months
		if months < 0 {
			years--
			months += 12
		}

		// Format tenure as a readable string
		if years > 0 {
			if months > 0 {
				tenure = fmt.Sprintf("%d years %d months", years, months)
			} else {
				if years == 1 {
					tenure = "1 year"
				} else {
					tenure = fmt.Sprintf("%d years", years)
				}
			}
		} else if months > 0 {
			if months == 1 {
				tenure = "1 month"
			} else {
				tenure = fmt.Sprintf("%d months", months)
			}
		} else {
			totalDays := int(now.Sub(joinTime).Hours() / 24)
			if totalDays == 1 {
				tenure = "1 day"
			} else {
				tenure = fmt.Sprintf("%d days", totalDays)
			}
		}
	}
	return tenure
}
