package usecase

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/domain"
	"golang.org/x/crypto/bcrypt"
)

// processEmployeeData processes and validates employee data from Excel
func (e *employeeUseCase) processEmployeeData(employee domain.ExcelEmployeeData, adminID int) (domain.ProcessedEmployeeData, error) {
	var processed domain.ProcessedEmployeeData
	processed.ExcelEmployeeData = employee

	// Find employee type by name
	employeeType, err := e.employeeRepository.FindEmployeeTypeByName(employee.TypeName, adminID)
	if err != nil {
		return processed, fmt.Errorf("error finding employee type: %v", err)
	}
	if employeeType == nil {
		return processed, fmt.Errorf("employee type '%s' not found", employee.TypeName)
	}
	processed.TypeID = employeeType.TypeID

	// Find jabatan by name
	jabatan, err := e.employeeRepository.FindJabatanByName(employee.JabatanName)
	if err != nil {
		return processed, fmt.Errorf("error finding jabatan: %v", err)
	}
	if jabatan == nil || jabatan.JabatanID == 0 {
		return processed, fmt.Errorf("jabatan '%s' not found", employee.JabatanName)
	}
	processed.JabatanID = jabatan.JabatanID

	// Find outlet by name (optional)
	if employee.OutletName != "" {
		outlet, err := e.employeeRepository.FindOutletByName(employee.OutletName, adminID)
		if err != nil {
			return processed, fmt.Errorf("error finding outlet: %v", err)
		}
		if outlet == nil {
			return processed, fmt.Errorf("outlet '%s' not found", employee.OutletName)
		}
		processed.OutletID = outlet.OutletID
	}

	// Parse date join
	dateJoinUnix, err := parseDateToUnix(employee.DateJoin)
	if err != nil {
		return processed, fmt.Errorf("error parsing date join: %v", err)
	}
	processed.DateJoinUnix = dateJoinUnix

	// Parse salary
	salaryInt, err := parseSalary(employee.Salary)
	if err != nil {
		return processed, fmt.Errorf("error parsing salary: %v", err)
	}
	processed.SalaryInt = salaryInt

	// Check if employee exists
	existingEmployee, err := e.employeeRepository.FindEmployeeByNIK(employee.NIK, adminID)
	if err != nil {
		return processed, fmt.Errorf("error checking existing employee: %v", err)
	}
	processed.IsNewEmployee = existingEmployee == nil

	// Handle account management
	accountID, isNewAccount, err := e.handleAccountManagement(employee.Email, employee.Name, employee.Phone)
	if err != nil {
		return processed, fmt.Errorf("error handling account: %v", err)
	}
	processed.AccountID = accountID
	processed.IsNewAccount = isNewAccount

	return processed, nil
}

// handleAccountManagement finds or creates an account for the employee
func (e *employeeUseCase) handleAccountManagement(email, name, phone string) (int64, bool, error) {
	// Check if account exists
	existingAccount, err := e.employeeRepository.FindAccountByEmail(email)
	if err != nil {
		return 0, false, err
	}

	if existingAccount != nil {
		// Account exists, return its ID
		return existingAccount.ID, false, nil
	}

	// Create new account
	// Generate a default password (can be changed later)
	defaultPassword := "password123"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(defaultPassword), bcrypt.DefaultCost)
	if err != nil {
		return 0, false, fmt.Errorf("error hashing password: %v", err)
	}

	newAccount := domain.Account{
		Name:      name,
		Phone:     phone,
		Email:     email,
		Password:  string(hashedPassword),
		CreatedAt: time.Now().UnixMilli(),
		UpdatedAt: time.Now().UnixMilli(),
		IsActive:  true,
	}

	accountID, err := e.employeeRepository.CreateAccount(newAccount)
	if err != nil {
		return 0, false, fmt.Errorf("error creating account: %v", err)
	}

	return accountID, true, nil
}

// upsertEmployeeFromExcel inserts or updates employee data
func (e *employeeUseCase) upsertEmployeeFromExcel(processed domain.ProcessedEmployeeData, adminID int) (int64, error) {
	// Convert processed data to EmployeeData
	employeeData := domain.EmployeeData{
		Nik:             processed.NIK,
		Name:            processed.Name,
		Address:         processed.Address,
		Phone:           processed.Phone,
		Email:           processed.Email,
		TypeFkid:        strconv.Itoa(processed.TypeID),
		JabatanId:       strconv.Itoa(processed.JabatanID),
		JoinDate:        strconv.FormatInt(processed.DateJoinUnix, 10),
		EmployeeSallary: processed.SalaryInt,
		MaxLeave:        strconv.Itoa(processed.MaxLeave),
		Npwp:            processed.NPWP,
		AdminId:         adminID, // Use the actual admin ID
		AccountId:       processed.AccountID,
		DataCreated:     int(time.Now().UnixMilli()),
		DataModified:    int(time.Now().UnixMilli()),
	}

	// Set outlet if provided
	if processed.OutletID > 0 {
		employeeData.OutletIds = strconv.Itoa(processed.OutletID)
	}

	// Upsert employee
	hrmEmployeeID, err := e.employeeRepository.UpsertEmployee(employeeData, !processed.IsNewEmployee)
	if err != nil {
		return 0, fmt.Errorf("error upserting employee: %v", err)
	}

	// Handle additional info
	if len(processed.AdditionalInfo) > 0 {
		err = e.handleAdditionalInfo(int(hrmEmployeeID), processed.AdditionalInfo)
		if err != nil {
			log.IfError(err)
			// Don't fail the entire operation for additional info errors
		}
	}

	return hrmEmployeeID, nil
}

// handleAdditionalInfo manages additional employee information
func (e *employeeUseCase) handleAdditionalInfo(hrmEmployeeID int, additionalInfo map[string]string) error {
	// Delete existing additional info
	err := e.employeeRepository.DeleteAdditionalInfo(hrmEmployeeID)
	if err != nil {
		return fmt.Errorf("error deleting existing additional info: %v", err)
	}

	// Insert new additional info
	var addInfoData []map[string]any
	for title, info := range additionalInfo {
		if strings.TrimSpace(info) != "" { // Only add non-empty values
			addInfoData = append(addInfoData, map[string]any{
				"hrm_employee_fkid": hrmEmployeeID,
				"title":             title,
				"info":              info,
			})
		}
	}

	if len(addInfoData) > 0 {
		err = e.employeeRepository.AddInfo(addInfoData)
		if err != nil {
			return fmt.Errorf("error adding additional info: %v", err)
		}
	}

	return nil
}

// parseExcelFileWithAdditionalInfo parses Excel file including additional info columns
func (e *employeeUseCase) parseExcelFileWithAdditionalInfo(filePath string) ([]domain.ExcelEmployeeData, []domain.ExcelImportError, []string, error) {
	// This is an enhanced version that also returns additional info column headers
	employees, errors, err := e.parseExcelFile(filePath)
	if err != nil {
		return nil, nil, nil, err
	}

	// For now, return empty additional headers - this would be enhanced to parse them from the Excel file
	var additionalHeaders []string

	return employees, errors, additionalHeaders, nil
}

// batchProcessEmployeeData processes and validates all employee data in batches
func (e *employeeUseCase) batchProcessEmployeeData(employees []domain.ExcelEmployeeData, adminID int) ([]domain.ProcessedEmployeeData, []domain.ExcelImportError, error) {
	log.Info("Starting batch processing of %d employees", len(employees))

	// Extract unique values for batch queries
	uniqueTypeNames := make(map[string]bool)
	uniqueJabatanNames := make(map[string]bool)
	uniqueOutletNames := make(map[string]bool)
	uniqueNIKs := make(map[string]bool)
	uniqueEmails := make(map[string]bool)

	for _, emp := range employees {
		uniqueTypeNames[emp.TypeName] = true
		uniqueJabatanNames[emp.JabatanName] = true
		if emp.OutletName != "" {
			uniqueOutletNames[emp.OutletName] = true
		}
		uniqueNIKs[emp.NIK] = true
		uniqueEmails[emp.Email] = true
	}

	// Batch fetch all reference data
	log.Info("Fetching reference data in batches...")

	// Fetch employee types
	typeNames := make([]string, 0, len(uniqueTypeNames))
	for typeName := range uniqueTypeNames {
		typeNames = append(typeNames, typeName)
	}
	employeeTypes, err := e.employeeRepository.FindEmployeeTypesByNames(typeNames, adminID)
	if err != nil {
		return nil, nil, fmt.Errorf("error fetching employee types: %v", err)
	}
	typeMap := make(map[string]*domain.HrmMasterType)
	for _, empType := range employeeTypes {
		typeMap[empType.TypeName] = empType
	}

	// Fetch jabatans
	jabatanNames := make([]string, 0, len(uniqueJabatanNames))
	for jabatanName := range uniqueJabatanNames {
		jabatanNames = append(jabatanNames, jabatanName)
	}
	jabatans, err := e.employeeRepository.FindJabatansByNames(jabatanNames)
	if err != nil {
		return nil, nil, fmt.Errorf("error fetching jabatans: %v", err)
	}
	jabatanMap := make(map[string]*domain.EmployeesJabatan)
	for _, jabatan := range jabatans {
		jabatanMap[jabatan.Name] = jabatan
	}

	// Fetch outlets (if any)
	var outletMap map[string]*domain.Outlet
	if len(uniqueOutletNames) > 0 {
		outletNames := make([]string, 0, len(uniqueOutletNames))
		for outletName := range uniqueOutletNames {
			outletNames = append(outletNames, outletName)
		}
		outlets, err := e.employeeRepository.FindOutletsByNames(outletNames, adminID)
		if err != nil {
			return nil, nil, fmt.Errorf("error fetching outlets: %v", err)
		}
		outletMap = make(map[string]*domain.Outlet)
		for _, outlet := range outlets {
			outletMap[outlet.Name] = outlet
		}
	}

	// Fetch existing employees by NIK
	niks := make([]string, 0, len(uniqueNIKs))
	for nik := range uniqueNIKs {
		niks = append(niks, nik)
	}
	existingEmployees, err := e.employeeRepository.FindEmployeesByNIKs(niks, adminID)
	if err != nil {
		return nil, nil, fmt.Errorf("error fetching existing employees: %v", err)
	}
	employeeMap := make(map[string]*domain.Employee)
	for _, emp := range existingEmployees {
		employeeMap[emp.Nik] = emp
	}

	// Fetch existing accounts by email
	emails := make([]string, 0, len(uniqueEmails))
	for email := range uniqueEmails {
		emails = append(emails, email)
	}
	existingAccounts, err := e.employeeRepository.FindAccountsByEmails(emails)
	if err != nil {
		return nil, nil, fmt.Errorf("error fetching existing accounts: %v", err)
	}
	accountMap := make(map[string]*domain.Account)
	for _, acc := range existingAccounts {
		accountMap[acc.Email] = acc
	}

	log.Info("Reference data fetched. Processing employees...")

	// Process each employee using the cached data
	var processedEmployees []domain.ProcessedEmployeeData
	var validationErrors []domain.ExcelImportError

	for _, employee := range employees {
		processed, err := e.processEmployeeWithCache(employee, adminID, typeMap, jabatanMap, outletMap, employeeMap, accountMap)
		if err != nil {
			validationErrors = append(validationErrors, domain.ExcelImportError{
				Row:     employee.Row,
				Message: err.Error(),
				Value:   employee.NIK,
			})
			continue
		}
		processedEmployees = append(processedEmployees, processed)
	}

	log.Info("Batch processing completed: %d processed, %d errors", len(processedEmployees), len(validationErrors))
	return processedEmployees, validationErrors, nil
}

// processEmployeeWithCache processes a single employee using cached reference data
func (e *employeeUseCase) processEmployeeWithCache(
	employee domain.ExcelEmployeeData,
	adminID int,
	typeMap map[string]*domain.HrmMasterType,
	jabatanMap map[string]*domain.EmployeesJabatan,
	outletMap map[string]*domain.Outlet,
	employeeMap map[string]*domain.Employee,
	accountMap map[string]*domain.Account,
) (domain.ProcessedEmployeeData, error) {
	var processed domain.ProcessedEmployeeData
	processed.ExcelEmployeeData = employee

	// Validate employee type
	employeeType, exists := typeMap[employee.TypeName]
	if !exists || employeeType == nil {
		return processed, fmt.Errorf("employee type '%s' not found", employee.TypeName)
	}
	processed.TypeID = employeeType.TypeID

	// Validate jabatan
	jabatan, exists := jabatanMap[employee.JabatanName]
	if !exists || jabatan == nil {
		return processed, fmt.Errorf("jabatan '%s' not found", employee.JabatanName)
	}
	processed.JabatanID = jabatan.JabatanID

	// Validate outlet (optional)
	if employee.OutletName != "" {
		outlet, exists := outletMap[employee.OutletName]
		if !exists || outlet == nil {
			return processed, fmt.Errorf("outlet '%s' not found", employee.OutletName)
		}
		processed.OutletID = outlet.OutletID
	}

	// Parse date join
	dateJoinUnix, err := parseDateToUnix(employee.DateJoin)
	if err != nil {
		return processed, fmt.Errorf("error parsing date join: %v", err)
	}
	processed.DateJoinUnix = dateJoinUnix

	// Parse salary
	salaryInt, err := parseSalary(employee.Salary)
	if err != nil {
		return processed, fmt.Errorf("error parsing salary: %v", err)
	}
	processed.SalaryInt = salaryInt

	// Check if employee exists
	existingEmployee, exists := employeeMap[employee.NIK]
	processed.IsNewEmployee = !exists || existingEmployee == nil

	// Handle account
	existingAccount, exists := accountMap[employee.Email]
	if exists && existingAccount != nil {
		processed.AccountID = existingAccount.ID
		processed.IsNewAccount = false
	} else {
		// Account will be created in batch
		processed.IsNewAccount = true
	}

	return processed, nil
}

// batchUpsertEmployees performs batch insert/update of employees within a transaction
func (e *employeeUseCase) batchUpsertEmployees(processedEmployees []domain.ProcessedEmployeeData, adminID int, response *domain.ExcelImportResponse) error {
	log.Info("Starting batch upsert of %d employees", len(processedEmployees))

	// Use the simplified batch upsert method in repository
	summary, err := e.employeeRepository.BatchUpsertEmployees(processedEmployees, adminID)
	if err != nil {
		return fmt.Errorf("failed to batch upsert employees: %v", err)
	}

	// Update response summary
	response.ImportSummary = *summary

	log.Info("Batch upsert completed successfully")
	return nil
}
