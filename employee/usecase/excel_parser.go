package usecase

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/domain"
)

// ExcelColumnMapping defines the expected column headers and their positions
type ExcelColumnMapping struct {
	NIK             int
	Name            int
	Type            int
	Email           int
	Phone           int
	Jabatan         int
	DateJoin        int
	Outlet          int
	MaxLeave        int
	Salary          int
	NPWP            int
	Address         int
	AdditionalStart int // Start index for additional info columns
}

// parseExcelFile parses the Excel file and returns employee data
func (e *employeeUseCase) parseExcelFile(filePath string) ([]domain.ExcelEmployeeData, []domain.ExcelImportError, error) {
	// Open Excel file
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to open Excel file: %v", err)
	}
	defer f.Close()

	// Get the first sheet
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		return nil, nil, fmt.Errorf("no sheets found in Excel file")
	}

	// Get all rows
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get rows: %v", err)
	}

	if len(rows) < 2 {
		return nil, nil, fmt.Errorf("excel file must have at least 2 rows (header + data), you have %d rows", len(rows))
	}

	headerRowIdx := 0
	//find header index start, find text starts with "NO"
	for i, row := range rows {
		if strings.HasPrefix(row[0], "NO") {
			headerRowIdx = i
			break
		}
	}
	log.Info("Header row index: %d", headerRowIdx)

	// Parse header row to determine column mapping
	mapping, errors := e.parseHeaderRow(rows[headerRowIdx])
	if len(errors) > 0 {
		return nil, errors, fmt.Errorf("header validation failed, %v", errors)
	}

	// Parse data rows
	var employees []domain.ExcelEmployeeData
	var allErrors []domain.ExcelImportError

	for i, row := range rows[headerRowIdx+1:] {
		rowNum := i + 2                                                                 // Excel row number (1-based, accounting for header)
		employee, rowErrors := e.parseDataRow(row, rowNum, mapping, rows[headerRowIdx]) // Pass header row for additional info

		if len(rowErrors) > 0 {
			allErrors = append(allErrors, rowErrors...)
		} else {
			employees = append(employees, employee)
		}
	}

	return employees, allErrors, nil
}

// parseHeaderRow validates and maps the header columns
func (e *employeeUseCase) parseHeaderRow(headerRow []string) (ExcelColumnMapping, []domain.ExcelImportError) {
	var mapping ExcelColumnMapping
	var errors []domain.ExcelImportError

	// Required columns mapping
	requiredColumns := map[string]*int{
		"NIK* (16 digit)": &mapping.NIK,
		"NAMA KARYAWAN*":  &mapping.Name,
		"TYPE*":           &mapping.Type,
		"EMAIL*":          &mapping.Email,
		"PHONE*":          &mapping.Phone,
		"JABATAN*":        &mapping.Jabatan,
		"TANGGAL MASUK*":  &mapping.DateJoin,
		"SALLARY*":        &mapping.Salary,
	}

	// Optional columns mapping
	optionalColumns := map[string]*int{
		"OUTLET":   &mapping.Outlet,
		"MAX CUTI": &mapping.MaxLeave,
		"NPWP":     &mapping.NPWP,
		"ALAMAT":   &mapping.Address,
	}

	// Initialize all mappings to -1 (not found)
	mapping.NIK = -1
	mapping.Name = -1
	mapping.Type = -1
	mapping.Email = -1
	mapping.Phone = -1
	mapping.Jabatan = -1
	mapping.DateJoin = -1
	mapping.Outlet = -1
	mapping.MaxLeave = -1
	mapping.Salary = -1
	mapping.NPWP = -1
	mapping.Address = -1
	mapping.AdditionalStart = -1

	// Map columns
	for i, header := range headerRow {
		header = strings.TrimSpace(header)

		// Check required columns
		if ptr, exists := requiredColumns[header]; exists {
			*ptr = i
		} else if ptr, exists := optionalColumns[header]; exists {
			*ptr = i
		} else if header == "ALAMAT" {
			mapping.Address = i
			mapping.AdditionalStart = i + 1 // Additional info starts after ALAMAT
		}
	}

	// Validate required columns are present
	for columnName, ptr := range requiredColumns {
		if *ptr == -1 {
			errors = append(errors, domain.ExcelImportError{
				Row:     1,
				Column:  columnName,
				Message: fmt.Sprintf("Required column '%s' not found", columnName),
			})
		}
	}

	// Set additional start if ALAMAT was found
	if mapping.Address != -1 && mapping.AdditionalStart == -1 {
		mapping.AdditionalStart = mapping.Address + 1
	}

	if len(errors) > 0 {
		log.Info("Header validation failed, headers: %v, errors: %v", headerRow, errors)
	}

	return mapping, errors
}

// parseDataRow parses a single data row
func (e *employeeUseCase) parseDataRow(row []string, rowNum int, mapping ExcelColumnMapping, headerRow []string) (domain.ExcelEmployeeData, []domain.ExcelImportError) {
	var employee domain.ExcelEmployeeData
	var errors []domain.ExcelImportError

	employee.Row = rowNum
	employee.AdditionalInfo = make(map[string]string)

	// Parse required fields
	if mapping.NIK >= 0 && mapping.NIK < len(row) {
		employee.NIK = strings.TrimSpace(row[mapping.NIK])
		if employee.NIK == "" {
			errors = append(errors, domain.ExcelImportError{
				Row: rowNum, Field: "NIK", Message: "NIK is required", Value: employee.NIK,
			})
		}
		// else if len(employee.NIK) != 16 {
		// 	errors = append(errors, domain.ExcelImportError{
		// 		Row: rowNum, Field: "NIK", Message: "NIK must be 16 digits", Value: employee.NIK,
		// 	})
		// }
	}

	if mapping.Name >= 0 && mapping.Name < len(row) {
		employee.Name = strings.TrimSpace(row[mapping.Name])
		if employee.Name == "" {
			errors = append(errors, domain.ExcelImportError{
				Row: rowNum, Field: "Name", Message: "Name is required", Value: employee.Name,
			})
		}
	}

	if mapping.Type >= 0 && mapping.Type < len(row) {
		employee.TypeName = strings.TrimSpace(row[mapping.Type])
		if employee.TypeName == "" {
			errors = append(errors, domain.ExcelImportError{
				Row: rowNum, Field: "Type", Message: "Type is required", Value: employee.TypeName,
			})
		}
	}

	if mapping.Email >= 0 && mapping.Email < len(row) {
		employee.Email = strings.TrimSpace(row[mapping.Email])
		if employee.Email == "" {
			errors = append(errors, domain.ExcelImportError{
				Row: rowNum, Field: "Email", Message: "Email is required", Value: employee.Email,
			})
		} else if !isValidEmail(employee.Email) {
			errors = append(errors, domain.ExcelImportError{
				Row: rowNum, Field: "Email", Message: "Invalid email format", Value: employee.Email,
			})
		}
	}

	if mapping.Phone >= 0 && mapping.Phone < len(row) {
		employee.Phone = strings.TrimSpace(row[mapping.Phone])
		if employee.Phone == "" {
			errors = append(errors, domain.ExcelImportError{
				Row: rowNum, Field: "Phone", Message: "Phone is required", Value: employee.Phone,
			})
		}
	}

	if mapping.Jabatan >= 0 && mapping.Jabatan < len(row) {
		employee.JabatanName = strings.TrimSpace(row[mapping.Jabatan])
		if employee.JabatanName == "" {
			errors = append(errors, domain.ExcelImportError{
				Row: rowNum, Field: "Jabatan", Message: "Jabatan is required", Value: employee.JabatanName,
			})
		}
	}

	if mapping.DateJoin >= 0 && mapping.DateJoin < len(row) {
		employee.DateJoin = strings.TrimSpace(row[mapping.DateJoin])
		if employee.DateJoin == "" {
			errors = append(errors, domain.ExcelImportError{
				Row: rowNum, Field: "DateJoin", Message: "Date join is required", Value: employee.DateJoin,
			})
		} else if !isValidDateFormat(employee.DateJoin) {
			errors = append(errors, domain.ExcelImportError{
				Row: rowNum, Field: "DateJoin", Message: "Invalid date format, expected YYYY/MM/DD", Value: employee.DateJoin,
			})
		}
	}

	if mapping.Salary >= 0 && mapping.Salary < len(row) {
		employee.Salary = strings.TrimSpace(row[mapping.Salary])
		if employee.Salary == "" {
			errors = append(errors, domain.ExcelImportError{
				Row: rowNum, Field: "Salary", Message: "Salary is required", Value: employee.Salary,
			})
		} else if !isValidSalaryFormat(employee.Salary) {
			errors = append(errors, domain.ExcelImportError{
				Row: rowNum, Field: "Salary", Message: "Invalid salary format", Value: employee.Salary,
			})
		}
	}

	// Parse optional fields
	if mapping.Outlet >= 0 && mapping.Outlet < len(row) {
		employee.OutletName = strings.TrimSpace(row[mapping.Outlet])
	}

	if mapping.MaxLeave >= 0 && mapping.MaxLeave < len(row) {
		maxLeaveStr := strings.TrimSpace(row[mapping.MaxLeave])
		if maxLeaveStr != "" {
			if maxLeave, err := strconv.Atoi(maxLeaveStr); err == nil {
				employee.MaxLeave = maxLeave
			} else {
				errors = append(errors, domain.ExcelImportError{
					Row: rowNum, Field: "MaxLeave", Message: "Max leave must be a number", Value: maxLeaveStr,
				})
			}
		}
	}

	if mapping.NPWP >= 0 && mapping.NPWP < len(row) {
		employee.NPWP = strings.TrimSpace(row[mapping.NPWP])
	}

	if mapping.Address >= 0 && mapping.Address < len(row) {
		employee.Address = strings.TrimSpace(row[mapping.Address])
	}

	// Parse additional info columns (after ALAMAT)
	if mapping.AdditionalStart >= 0 && mapping.AdditionalStart < len(headerRow) {
		for i := mapping.AdditionalStart; i < len(headerRow) && i < len(row); i++ {
			columnTitle := strings.TrimSpace(headerRow[i])
			columnValue := strings.TrimSpace(row[i])

			if columnTitle != "" && columnValue != "" {
				employee.AdditionalInfo[columnTitle] = columnValue
			}
		}
	}

	return employee, errors
}

// Helper functions for validation
func isValidEmail(email string) bool {
	return strings.Contains(email, "@") && strings.Contains(email, ".")
}

func isValidDateFormat(dateStr string) bool {
	_, err := time.Parse("2006/01/02", dateStr)
	return err == nil
}

func isValidSalaryFormat(salaryStr string) bool {
	_, err := parseSalary(salaryStr)
	return err == nil
}

// parseSalary converts formatted salary string to integer
func parseSalary(salaryStr string) (int, error) {
	cleaned := strings.ReplaceAll(salaryStr, ".", "")
	cleaned = strings.ReplaceAll(cleaned, ",", "")
	return strconv.Atoi(cleaned)
}

// parseDateToUnix converts YYYY/MM/DD format to Unix timestamp
func parseDateToUnix(dateStr string) (int64, error) {
	t, err := time.Parse("2006/01/02", dateStr)
	if err != nil {
		return 0, err
	}
	return t.Unix() * 1000, nil // Convert to milliseconds
}
