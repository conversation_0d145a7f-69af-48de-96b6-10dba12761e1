# build stage
FROM golang:alpine AS builder
#RUN apk --no-cache add build-base git bzr mercurial gcc
WORKDIR /src
COPY go.mod .
COPY go.sum .
RUN GO111MODULE=on go mod download
COPY . .
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o app main.go
RUN ls /usr/local/go/lib/time

#final stage
FROM alpine
COPY --from=builder src/app ./
COPY --from=builder /usr/local/go/lib/time/zoneinfo.zip /
COPY assets/templates/  /assets/templates/
ENV ZONEINFO=/zoneinfo.zip
ENTRYPOINT ["./app"]