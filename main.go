package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/swagger"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/bucket"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"gitlab.com/backend/api-hrm/core/util/fcmessage"
	"gitlab.com/backend/api-hrm/core/util/token"
	"gitlab.com/backend/api-hrm/docs"
	"gitlab.com/backend/api-hrm/domain"

	// jwtware "github.com/gofiber/jwt/v2"
	"github.com/joho/godotenv"
	"gitlab.com/backend/api-hrm/config"
	_ "gitlab.com/backend/api-hrm/docs"

	// cuti handler
	cutiHttp "gitlab.com/backend/api-hrm/cuti/delivery/http"
	cutiMysql "gitlab.com/backend/api-hrm/cuti/repository/mysql"
	cutiUseCase "gitlab.com/backend/api-hrm/cuti/usecase"

	// employee handler
	employeeHttp "gitlab.com/backend/api-hrm/employee/delivery/http"
	employeeMysql "gitlab.com/backend/api-hrm/employee/repository/mysql"
	employeeUseCase "gitlab.com/backend/api-hrm/employee/usecase"

	// shift handler
	shiftHttp "gitlab.com/backend/api-hrm/shift/delivery/http"
	shiftMysql "gitlab.com/backend/api-hrm/shift/repository/mysql"
	shiftUseCase "gitlab.com/backend/api-hrm/shift/usecase"

	// type handler
	typeHttp "gitlab.com/backend/api-hrm/master_type/delivery/http"
	typeMysql "gitlab.com/backend/api-hrm/master_type/repository/mysql"
	typeUseCase "gitlab.com/backend/api-hrm/master_type/usecase"

	// schedule handler
	scheduleHttp "gitlab.com/backend/api-hrm/schedule/delivery/http"
	scheduleMysql "gitlab.com/backend/api-hrm/schedule/repository/mysql"
	scheduleUseCase "gitlab.com/backend/api-hrm/schedule/usecase"

	// presensi handler
	presensiHttp "gitlab.com/backend/api-hrm/presensi/delivery/http"
	presensiMysql "gitlab.com/backend/api-hrm/presensi/repository/mysql"
	presensiUseCase "gitlab.com/backend/api-hrm/presensi/usecase"

	// messaging handler
	messageHttp "gitlab.com/backend/api-hrm/messaging/delivery/http"
	messageMysql "gitlab.com/backend/api-hrm/messaging/repository/mysql"
	messageUseCase "gitlab.com/backend/api-hrm/messaging/usecase"

	httpLoginEm "gitlab.com/backend/api-hrm/module/auth/delivery/http"
	loginEmRepo "gitlab.com/backend/api-hrm/module/auth/repository/mysql"
	LoginEmUsecase "gitlab.com/backend/api-hrm/module/auth/usecase"

	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	httpLogin "gitlab.com/backend/api-hrm/login/delivery/http"
	"gitlab.com/backend/api-hrm/login/repository/mysql"
	loginUseCase "gitlab.com/backend/api-hrm/login/usecase"
	httpProduct "gitlab.com/backend/api-hrm/product/delivery/http"
	productMysql "gitlab.com/backend/api-hrm/product/repository/mysql"
	"gitlab.com/backend/api-hrm/product/usecase"
)

func init() {
	err := godotenv.Load()
	if err != nil {
		fmt.Printf("can not load env from file: %v\n", err)
	}

	log.AddHook(&log.SlackHook{
		HookUrl: os.Getenv("SLACK_URL"),
		Channel: "api-hrm",
	})
}

// @title UNIQ HRM API
// @version 1.0
// @description API Documentation for UNIQ HRM System
// @host apis.uniqdev.web.id/hrm
// @BasePath /
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Enter the token with the 'Bearer ' prefix, e.g. 'Bearer abcde12345'
// @Security BearerAuth
func main() {
	dbConn := config.GetMySqlConn()
	defer config.CloseMySqlConn(dbConn)
	//gormConn := config.GetGormConn()
	authConf := config.GetAuthConfig()

	//productUseCase := usecase.NewProductUseCase(gorm.NewGormProductRepository(gormConn)) //use gorm
	productUseCase := usecase.NewProductUseCase(productMysql.NewMySqlProductRepository(dbConn)) //use native

	loginUseCase := loginUseCase.NewLoginUseCase(mysql.NewMysqlLoginRepository(dbConn), authConf)

	loginEm := LoginEmUsecase.AuthUseCase(loginEmRepo.AuthRepository(dbConn), authConf)

	// typeUseCase := typeUseCase.NewTypeUseCase(typeGorm.NewGormTypeRpository(gormConn))
	//http ------
	app := fiber.New()
	app.Use(logger.New())

	// Swagger
	swaggerDocUrl := os.Getenv("SWAGGER_DOC_URL")
	log.Info("swagger doc url: %v", swaggerDocUrl)
	swaggerHandler := swagger.New(swagger.Config{
		URL:          swaggerDocUrl, // The url pointing to API definition
		DeepLinking:  true,
		DocExpansion: "none",

		// BasePath:    "/",
		// Docs:       "./docs",
		// SwaggerFiles: swaggerFiles.Handler,
	})

	if swaggerHost := os.Getenv("SWAGGER_HOST"); swaggerHost != "" {
		docs.SwaggerInfo.Host = swaggerHost
		docs.SwaggerInfo.Schemes = []string{"http"}
	}

	if os.Getenv("ENV") == "development" || os.Getenv("ENV") == "localhost" {
		app.Get("/public/swagger/*", swaggerHandler)
	}

	// Default config
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
	}))

	// Or extend your config for customization
	// app.Use(cors.New(cors.Config{
	// 	AllowOrigins: "http://localhost:8080, https://uniqdev.tech, https://uniqdev.tech/hrm, https://staging.uniq.id, https://client.uniq.id",
	// 	AllowHeaders: "Origin, Content-Type, Accept, Authorization",
	// }))

	// app.Use(jwtware.New(jwtware.Config{
	// 	SigningKey:    authConf.PublicKey,
	// 	SigningMethod: authConf.MethodString,
	// 	Filter: func(ctx *fiber.Ctx) bool {
	// 		return ctx.OriginalURL() == "/auth/login"
	// 	},
	// }))

	// httpLoginEm.AuthHandler(app, loginEm)

	//manual JWT token decode
	app.Use(func(c *fiber.Ctx) error {
		requestToken := c.Get("Authorization")
		tokenData := strings.Split(requestToken, ".")
		if len(tokenData) != 3 {
			return c.SendStatus(401)
		}

		//get token data
		tokenBody := tokenData[1] //jwt's payload

		//decode token
		decoded, _ := base64.RawStdEncoding.DecodeString(tokenBody)
		decodedJson := string(decoded)
		claims, _ := cast.JsonToMap(decodedJson)
		claims = claims["data"].(map[string]interface{})
		dataUser, _ := json.Marshal(claims["user"])
		dataRole, _ := json.Marshal(claims["user_role"])

		//share token data
		var (
			user domain.User
			role domain.UserRole
		)

		// token data :: user
		err := json.Unmarshal(dataUser, &user)
		if err != nil {
			log.Info("err: %v", err)
		}
		token.UserData = user

		// token data :: user_role
		err = json.Unmarshal(dataRole, &role)
		if err != nil {
			log.Info("err: %v", err)
		}
		token.UserRole = role

		//set user data in header request
		c.Request().Header.Set("user_id", user.UserId)
		c.Request().Header.Set("user_type", user.UserType)
		c.Request().Header.Set("business_id", user.BusinessId)
		c.Request().Header.Set("outlet_access", role.OutletAccess)

		if user.UserId == "" {
			fmt.Println("invalid token on manual decode token")
			return c.SendStatus(401)
		}

		return c.Next()
	})

	// app.Use(func(c *fiber.Ctx) error {
	// 	requestToken := c.Get("Authorization")
	// 	tokenData := strings.Split(requestToken, ".")
	// 	if len(tokenData) == 3 {
	// 		tokenBody := tokenData[1]

	// 		//get token data
	// 		decoded, _ := base64.RawStdEncoding.DecodeString(tokenBody)
	// 		decodedJson := string(decoded)
	// 		claims, _ := cast.JsonToMap(decodedJson)
	// 		dataUser, _ := json.Marshal(claims["data"])
	// 		dataRole, _ := json.Marshal(claims["role"])

	// 		//share token user data
	// 		var user domain.User
	// 		cast.JsonToStruct(dataUser, &user)
	// 		token.UserData = user
	// 		//c.Request().SetBody()

	// 		//share token user role data

	// 		var role domain.UserRole
	// 		cast.JsonToStruct(dataRole, &role)
	// 		token.UserRole = role

	// 		//set header request
	// 		c.Request().Header.Set("user_id", user.UserId)
	// 	}

	// 	if token.UserData.UserId == "" {
	// 		fmt.Println("invalid token on manual decode token")
	// 		return c.SendStatus(401)
	// 	}
	// 	return c.Next()
	// })

	httpLoginEm.AuthHandler(app, loginEm)

	httpProduct.NewProductHandler(app, productUseCase)
	httpLogin.NewLoginHandler(app, loginUseCase)
	// type handler
	typeHttp.NewTypeHandler(app, typeUseCase.NewTypeUseCase(typeMysql.NewMySQLTypeRepository(dbConn)))
	// shift handler
	shiftHttp.NewShiftHandler(app, shiftUseCase.NewShiftUseCase(shiftMysql.NewSQLShiftRepository(dbConn)))
	// hrm employee handler
	employeeHttp.NewEmployeeHandler(app, employeeUseCase.NewEmployeeUseCase(employeeMysql.NewMySQLEmployeeRepository(dbConn)))
	// schedule handler
	scheduleHttp.NewScheduleHandler(app, scheduleUseCase.NewScheduleUseCase(scheduleMysql.NewMySQLScheduleRepository(dbConn)))
	// cuti handler
	cutiHttp.NewCutiHandler(app, cutiUseCase.NewCutiUseCase(cutiMysql.NewMySQLCutiRepository(dbConn)))
	// presensi handler
	presensiHttp.NewPresensiHandler(app, presensiUseCase.NewPresensiUseCase(presensiMysql.NewMySQLPresensiRepository(dbConn)))
	// messaging handler
	messageHttp.NewMessagingHandler(app, messageUseCase.NewMessagingUseCase(messageMysql.NewMySQLMessagingRepository(dbConn)))

	if len(os.Args) > 3 && os.Args[1] == "--test" {
		switch os.Args[2] {
		case "delete-attachment":
			bucket.DeleteBucket("uniq-187911.appspot.com", os.Args[3], true)
		default:
			fmt.Println("no test case")
		}

		str := "https://storage.googleapis.com/uniq-187911.appspot.com/xxx"
		str = strings.TrimPrefix(str, "https://storage.googleapis.com/uniq-187911.appspot.com/")
		fmt.Println(str)
		panic(fmt.Errorf("for testing... %v", os.Args[2]))
	}

	defer fmt.Println("service ended...")
	errs := make(chan error)

	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		errs <- fmt.Errorf("%s", <-c)
	}()

	port := os.Getenv("PORT")
	if port == "" {
		port = "80"
	}
	go func() {
		fmt.Printf("running on port :%s", port)
		errs <- app.Listen(fmt.Sprintf(":%s", port))
	}()

	// u, _ := json.MarshalIndent(app.Stack(), "", "  ")
	// fmt.Println(string(u))

	//if param --test token
	if len(os.Args) > 2 && os.Args[1] == "--test" {
		switch os.Args[2] {
		case "token":
			fmt.Println("testing token")
			fcmessage.SendMessage("test title", "test body", map[string]string{"message_id": "123"}, "fTMfFNqgRzGTEQLtm_6ckE:APA91bH5CwEgFhJjS3Ar_x4TXJexT0-JHktBgZSV3HXMq2w8cfaL0kmlPuRHr122PVyqIkn3vUhOKSuI-a1YCkrv6UjtYKQ8zkbjK6GwMqxtq9OZeZRCmUg", "eHu2QuSkRMSNzfZaTNW6on:APA91bHhdEFIYN6DAgufGGJ3SCQFUS0ct3X60FFuHmfNP3DvCJaPid7Vlh0S-TFf7wN32gpNwHc9AcZn-k1tVEzwdCeK9ZKdUyeZ0_JEORQbo2ddXu78HMQ")
		default:
			fmt.Println("no test case")
		}
		panic(fmt.Errorf("for testing... %v", os.Args[2]))
	}

	fmt.Println("exit", <-errs)
}
