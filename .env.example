#development,staging,production
ENV=development

SWAGGER_DOC_URL=doc.json

#db setting
DB_USERNAME=db_user
DB_PASSWORD=db_pass
DB_HOST=db_ip
DB_PORT=3306

API_URL=api-ai-python
MESSAGER_URL=url-messenger-email
MESSAGER_TOKEN=token-api-messenger
EMAIL_URL=email-url

#app setting
PORT=3000
PRIVATE_KEY_PATH=assets/rsa/priv.key
PUBLIC_KEY_PATH=assets/rsa/pub.key

#not required
SLACK_URL=https://hooks.slack.com/services/xxxx

FCM_PATH=assets/rsa/firebase.json