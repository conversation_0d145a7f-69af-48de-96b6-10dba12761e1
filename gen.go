package main

import (
	"bufio"
	"flag"
	"fmt"
	"io/ioutil"
	"os"
	"regexp"
	"strings"

	. "github.com/dave/jennifer/jen"
)

func maint() {
	featureName := flag.String("f", "", "feature name")
	delivery := flag.String("d", "http", "delivery protocol")
	repository := flag.String("r", "mysql", "database framework")

	flag.Parse()
	camelCase("MasterEmployee")

	if *featureName == "" {
		fmt.Println("feature name is required, you can run the program with: -f [FEATURE]")
		fmt.Print("what is the feature name: ")
		input := bufio.NewScanner(os.Stdin)
		input.Scan()
		*featureName = input.Text()
	}

	//read go mod
	mod, err := ioutil.ReadFile("go.mod")
	if err != nil {
		panic(err)
	}
	modules := regexp.MustCompile(`^module ([a-z\/\.-]+)`).FindStringSubmatch(string(mod))
	if len(modules) == 0 {
		panic("failed finding module name")
	}

	//cmd := exec.Command("go","list")
	moduleId := modules[1] + "/"
	*featureName = strings.Replace(*featureName, " ", "", -1)
	feature := camelCase(*featureName)
	featureFile := snakeCase(feature)

	domain := NewFile("domain")
	domain.Type().Id(strings.Title(feature)).Struct()
	domain.Type().Id(fmt.Sprintf("%sUseCase", strings.Title(feature))).Interface()
	domain.Type().Id(fmt.Sprintf("%sRepository", strings.Title(feature))).Interface()

	isCreateFile := true
	domainFilePath := fmt.Sprintf("domain/%s.go", featureFile)
	if exists(domainFilePath) {
		fmt.Printf("domain %s is already exist, replace it? y/n ", featureFile)
		input := bufio.NewScanner(os.Stdin)
		input.Scan()
		isCreateFile = strings.ToLower(input.Text()) == "y"
	}
	if isCreateFile {
		fmt.Println("creating domain file...")
		err := domain.Save(domainFilePath)
		if err != nil {
			panic(err)
		}
	}

	dl := NewFile(strings.ToLower(*delivery))
	dl.Type().Id(fmt.Sprintf("%sHandler", feature)).Struct(
		Id("").Qual(moduleId+"domain", fmt.Sprintf("%sUseCase", strings.Title(feature))),
	)

	dl.Func().Id(fmt.Sprintf("New%s%sHandler", strings.Title(*delivery), strings.Title(feature))).
		Params(
			Id("app").Op("*").Qual("github.com/gofiber/fiber/v2", "App"),
			Id("useCase").Qual(moduleId+"domain", fmt.Sprintf("%sUseCase", strings.Title(feature))),
		).
		Block(
			Id("handler").Op(":=").Op("&").Id(fmt.Sprintf("%sHandler", feature)).Values(Id("useCase")),
		)

	deliveryPath := fmt.Sprintf("%s/delivery/%s", featureFile, strings.ToLower(*delivery))
	err = os.MkdirAll(deliveryPath, os.ModePerm)
	if err != nil {
		panic(err)
	}

	deliveryFilePath := fmt.Sprintf("%s/%s_handler.go", deliveryPath, featureFile)
	isCreateFile = true
	if exists(deliveryFilePath) {
		fmt.Printf("delivery %s is already exist, replace it? y/n ", featureFile)
		input := bufio.NewScanner(os.Stdin)
		input.Scan()
		isCreateFile = strings.ToLower(input.Text()) == "y"
	}

	if isCreateFile {
		fmt.Println("creating delivery file...")
		err = dl.Save(deliveryFilePath)
		if err != nil {
			panic(err)
		}
	}

	repo := NewFile(strings.ToLower(*repository))
	repo.Type().Id(fmt.Sprintf("%sRepository", feature)).Struct(
		Id("").Qual(moduleId+"core/mysql", "Repository"),
	)

	repo.Func().Id(fmt.Sprintf("New%s%sRepository", strings.Title(*repository), strings.Title(feature))).
		Params(Id("db").Op("*").Qual("database/sql", "DB")).
		Qual(moduleId+"domain", fmt.Sprintf("%sRepository", strings.Title(feature))).
		Block(
			Return(Op("&").Id(fmt.Sprintf("%sRepository", feature)).Values(Qual(moduleId+"core/mysql", "Repository").Values(Dict{
				Id("Conn"): Id("db"),
			}))),
		)

	repoPath := fmt.Sprintf("%s/repository/%s", featureFile, strings.ToLower(*repository))
	err = os.MkdirAll(repoPath, os.ModePerm)
	if err != nil {
		panic(err)
	}

	isCreateFile = true
	repoFilePath := fmt.Sprintf("%s/%s_repository.go", repoPath, featureFile)
	if exists(repoFilePath) {
		fmt.Printf("repository %s is already exist, replace it? y/n ", featureFile)
		input := bufio.NewScanner(os.Stdin)
		input.Scan()
		isCreateFile = strings.ToLower(input.Text()) == "y"
	}
	if isCreateFile {
		fmt.Println("creating repository file...")
		err = repo.Save(repoFilePath)
		if err != nil {
			panic(err)
		}
	}

	//generate use case
	uc := NewFile("usecase")
	uc.Type().Id(fmt.Sprintf("%sUseCase", feature)).Struct(Qual(moduleId+"domain", strings.Title(feature)+"Repository"))
	uc.Func().Id(fmt.Sprintf("New%sUseCase", strings.Title(feature))).
		Params(Id("repository").Qual(moduleId+"domain", fmt.Sprintf("%sRepository", strings.Title(feature)))).
		Qual(moduleId+"domain", fmt.Sprintf("%sUseCase", strings.Title(feature))).
		Block(Return(Op("&").Id(fmt.Sprintf("%sUseCase", feature))).Values(Id("repository")))

	useCasePath := fmt.Sprintf("%s/usecase", featureFile)
	err = os.MkdirAll(useCasePath, os.ModePerm)
	if err != nil {
		panic(err)
	}

	isCreateFile = true

	useCaseFilePath := fmt.Sprintf("%s/%s_usecase.go", useCasePath, featureFile)
	if exists(useCaseFilePath) {
		fmt.Printf("usecase %s is already exist, replace it? y/n ", featureFile)
		input := bufio.NewScanner(os.Stdin)
		input.Scan()
		isCreateFile = strings.ToLower(input.Text()) == "y"
	}

	if isCreateFile {
		fmt.Println("creating usecase file...")
		err = uc.Save(useCaseFilePath)
		if err != nil {
			panic(err)
		}
	}

	//fmt.Printf("%#v", domain)
	//fmt.Printf("%#v", dl)
	//fmt.Printf("%#v", repo)
	//fmt.Printf("%#v", uc)
	fmt.Println("all files generated successfully")
}

var matchFirstCap = regexp.MustCompile("(.)([A-Z][a-z]+)")
var matchAllCap = regexp.MustCompile("([a-z0-9])([A-Z])")

func snakeCase(str string) string {
	snake := matchFirstCap.ReplaceAllString(str, "${1}_${2}")
	snake = matchAllCap.ReplaceAllString(snake, "${1}_${2}")
	return strings.ToLower(snake)
}

var matchUpper = regexp.MustCompile("^[A-Z]")
var matchUnder = regexp.MustCompile("(_)([a-zA-Z])")
var matchUnderAll = regexp.MustCompile("(_)([A-Z])")

func camelCase(str string) string {
	camel := matchUpper.ReplaceAllStringFunc(str, strings.ToLower)
	camel = matchUnder.ReplaceAllStringFunc(camel, strings.ToUpper)
	camel = matchUnderAll.ReplaceAllString(camel, strings.ToUpper("${2}"))
	return camel
}

func exists(name string) bool {
	if _, err := os.Stat(name); err != nil {
		if os.IsNotExist(err) {
			return false
		}
	}
	return true
}
