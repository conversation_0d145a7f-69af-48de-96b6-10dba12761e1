package http

import (
	"fmt"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"gitlab.com/backend/api-hrm/domain"
)

type ProductHandler struct {
	ProductUseCase domain.ProductUseCase
}

func NewProductHandler(app *fiber.App, uc domain.ProductUseCase) {
	handler := &ProductHandler{ProductUseCase: uc}

	app.Get("/v1/product", handler.Fetch)
	app.Get("/v1/product/:id", handler.FetchById)
}

func (p *ProductHandler) Fetch(c *fiber.Ctx) error {
	products, err := p.ProductUseCase.Fetch()
	if err != nil {
		fmt.Println("error: ", err)
		return nil
	}

	return c.JSON(products)
}

func (p *ProductHandler) FetchById(c *fiber.Ctx) error {
	id := c.Params("id")
	fmt.Println(id)

	product, err := p.ProductUseCase.FetchById(cast.ToInt64(id))
	if err != nil {
		fmt.Println("error: ", err)
		return nil
	}

	return c.JSON(product)
}
