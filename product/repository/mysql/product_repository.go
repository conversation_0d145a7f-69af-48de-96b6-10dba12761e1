package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/mitchellh/mapstructure"
	"gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySqlProductRepository struct {
	mysql.Repository
}

func NewMySqlProductRepository(conn *sql.DB) domain.ProductRepository {
	return &mySqlProductRepository{mysql.Repository{Conn: conn}}
}

func (m *mySqlProductRepository) Fetch() ([]domain.Product, error) {
	resp, err := m.QueryArrayOld("select * from products")
	if err != nil {
		log.Printf("getting product error: %v\n", err)
		return nil, err
	}

	log.Printf("total data : %d", len(resp))

	bigBang := time.Now()
	start := time.Now()
	respJson, err := json.Marshal(resp)
	if err != nil {
		log.Printf("Marshal error: %v\n", err)
	}
	log.Printf("map to json took : %v\n", time.Since(start))

	start = time.Now()
	var result []domain.Product
	err = json.Unmarshal(respJson, &result)
	if err != nil {
		log.Printf("Unmarshal error: %v\n", err)
	}

	log.Printf("json to struct took : %v\n\n", time.Since(start))
	log.Printf("total took : %v", time.Since(bigBang))

	start = time.Now()
	var result2 []domain.Product
	err = mapstructure.Decode(resp, &result2)
	if err != nil {
		log.Printf("mapstructure error: %v\n", err)
	}
	log.Printf("using lib took : %v\n", time.Since(start))

	log.Printf("total data : %d", len(result2))

	return result, nil
}

func (m *mySqlProductRepository) FetchById(id int64) (domain.Product, error) {
	var result domain.Product
	var results []domain.Product

	err := m.Query("select * from products where product_id < 300").Model(&results)
	if err != nil {
		fmt.Println("get product error", err)
	}
	fmt.Println("data : ", results)

	return result, nil
}

func (m *mySqlProductRepository) Add(product domain.Product) error {
	_, err := m.Insert("product", map[string]interface{}{
		"name":       product.Name,
		"product_id": product.ProductId,
	})
	return err
}
