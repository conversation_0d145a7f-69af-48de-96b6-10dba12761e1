package gorm

import (
	"errors"

	"gitlab.com/backend/api-hrm/domain"
	"gorm.io/gorm"
)

type gormProductRepository struct {
	db *gorm.DB
}

func NewGormProductRepository(conn *gorm.DB) domain.ProductRepository {
	return &gormProductRepository{db: conn}
}

func gormErr(err error) error {
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil
	}
	return err
}

func (p *gormProductRepository) Fetch() ([]domain.Product, error) {
	var products []domain.Product
	err := p.db.Find(&products).Error
	return products, gormErr(err)
}

func (p *gormProductRepository) FetchById(id int64) (domain.Product, error) {
	var product domain.Product
	err := p.db.First(&product, id).Error
	return product, gormErr(err)
}

func (p *gormProductRepository) Add(product domain.Product) error {
	return p.db.Create(&product).Error
}
