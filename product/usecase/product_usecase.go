package usecase

import "gitlab.com/backend/api-hrm/domain"

type productUseCase struct {
	productRepository domain.ProductRepository
}

func NewProductUseCase(pr domain.ProductRepository) domain.ProductUseCase {
	return &productUseCase{productRepository: pr}
}

func (p *productUseCase) Fetch() ([]domain.Product, error) {
	allProducts, err := p.productRepository.Fetch()
	if err != nil {
		return nil, err
	}
	return allProducts, nil
}

func (p *productUseCase) FetchById(id int64) (domain.Product, error) {
	return p.productRepository.FetchById(id)
}
