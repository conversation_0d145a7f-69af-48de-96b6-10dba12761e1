package domain

// LoanType represents the hrm_loan_types table structure
type LoanType struct {
	TypesID      int64   `json:"types_id" db:"types_id"`
	Name         string  `json:"name" db:"name"`
	Principal    float64 `json:"principal" db:"principal"`
	InterestRate float64 `json:"interest_rate" db:"interest_rate"`
	TermMonths   int     `json:"term_months" db:"term_months"`
	AdminFkid    int     `json:"admin_fkid" db:"admin_fkid"`
	CreatedAt    int64   `json:"created_at" db:"created_at"`
	UpdatedAt    int64   `json:"updated_at" db:"updated_at"`
}

// LoanContract interface defines the basic operations for loan types
type LoanContract interface {
	FetchLoanTypes(adminFkid int) ([]LoanType, error)
}

// LoanUseCase interface defines the business logic operations
type LoanUseCase interface {
	LoanContract
}

// LoanRepository interface defines the data access operations
type LoanRepository interface {
	LoanContract
}
