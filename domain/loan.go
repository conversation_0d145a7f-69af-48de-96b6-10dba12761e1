package domain

// LoanType represents the hrm_loan_types table structure
type LoanType struct {
	LoanTypeID   int64   `json:"loan_type_id" db:"types_id"`
	LoanTypeName string  `json:"loan_type_name" db:"name"`
	Principal    float64 `json:"principal" db:"principal"`
	InterestRate float64 `json:"interest_rate" db:"interest_rate"`
	TermMonths   int     `json:"term_months" db:"term_months"`
	AdminFkid    int     `json:"admin_fkid" db:"admin_fkid"`
	CreatedAt    int64   `json:"created_at" db:"created_at"`
	UpdatedAt    int64   `json:"updated_at" db:"updated_at"`
}

// Loan represents the hrm_loans table structure
type Loan struct {
	LoanID           int64   `json:"loan_id" db:"loan_id"`
	HrmEmployeeFkid  int     `json:"hrm_employee_fkid" db:"hrm_employee_fkid"`
	LoanTypeFkid     int64   `json:"loan_type_fkid" db:"loan_type_fkid"`
	Principal        float64 `json:"principal" db:"principal"`
	InterestRate     float64 `json:"interest_rate" db:"interest_rate"`
	TermMonths       int     `json:"term_months" db:"term_months"`
	FirstDueDate     string  `json:"first_due_date" db:"first_due_date"`
	TotalDue         float64 `json:"total_due" db:"total_due"`
	OutstandingTotal float64 `json:"outstanding_total" db:"outstanding_total"`
	Notes            string  `json:"notes" db:"notes"`
	Status           string  `json:"status" db:"status"`
	CreatedAt        int64   `json:"created_at" db:"created_at"`
	UpdatedAt        int64   `json:"updated_at" db:"updated_at"`
}

// LoanRequest represents the request payload for creating a new loan
type LoanRequest struct {
	HrmEmployeeFkid int     `json:"hrm_employee_fkid" validate:"required"`
	LoanTypeName    string  `json:"loan_type_name" validate:"required"`
	Principal       float64 `json:"principal" validate:"required,gt=0"`
	InterestRate    float64 `json:"interest_rate" validate:"gte=0"`
	TermMonths      int     `json:"term_months" validate:"required,gt=0"`
	FirstDueDate    string  `json:"first_due_date" validate:"required"`
	Notes           string  `json:"notes"`
}

// LoanContract interface defines the basic operations for loans
type LoanContract interface {
	FetchLoanTypes(adminFkid int) ([]LoanType, error)
	AddLoan(loan LoanRequest, adminFkid int) (int64, error)
}

// LoanUseCase interface defines the business logic operations
type LoanUseCase interface {
	LoanContract
}

// LoanRepository interface defines the data access operations
type LoanRepository interface {
	LoanContract
	FindOrCreateLoanType(loan LoanRequest, adminFkid int) (int64, error)
}
