package domain

type Message struct {
	NotificationID   int    `json:"notification_id"`
	Title            string `json:"title"`
	Message          string `json:"message"`
	IsRead           int    `json:"is_read"`
	IsViewed         int    `json:"is_viewed"`
	DataCreated      int    `json:"data_created"`
	Type             string `json:"type"`
	ReceiverID       string `json:"receiver_id"`
	AdminFkid        int    `json:"admin_fkid"`
	NotificationType string `json:"notification_type"`
	NotificationData string `json:"notification_data"`
}

type Device struct {
	ID         int    `json:"id"`
	UserType   string `json:"user_type"`
	UserID     string `json:"user_id"`
	Device     string `json:"device"`
	DeviceInfo string `json:"device_info"`
	Token      string `json:"token"`
}

type DeviceCreateRequest struct {
	Device     string `form:"device" json:"device" validate:"required"`
	DeviceInfo string `form:"device_info" json:"device_info" validate:"required"` // JSON format device information
	Token      string `form:"token" json:"token" validate:"required"`
}

type MessagingContract interface {
	AddMessage(message map[string]interface{}) error
	AddDeviceInfo(info DeviceCreateRequest, userType string, userID string) error
	FetchUserDevice(empID int) ([]Device, error)
	FetchUserDeviceByToken(token string) (Device, error)
	FetchMessages(empID int, tipe string) ([]Message, error)
	UpdateMessageView(data []map[string]interface{}) error
	UpdateMessageRead(data []map[string]interface{}) error
	DeleteNotif(notifID int) error
	DeleteMessages(msgID []string) error
	UpdateUserDevice(data map[string]interface{}, where string) error
}

type MessagingUseCase interface {
	MessagingContract
}

type MessagingRepository interface {
	MessagingContract
}
