package domain

import (
	"github.com/gofiber/fiber/v2"
	"gitlab.com/backend/api-hrm/core/util/cast"
)

type Auth struct{}
type AuthUseCase interface {
	CreateToken(AuthInputLogin) (UserToken, error)
}
type AuthRepository interface {
	FindAccount(userType, email string) (User, string, error)
	GetOutletAccess(user User) (string, error)
}

type UserRole struct {
	OutletAccess string
}

type AuthInputLogin struct {
	Email    string `json:"email" xml:"email" form:"email" validate:"required,email"`
	Password string `json:"password" xml:"password" form:"password" validate:"required"`
}

type UserSession struct {
	UserId       int    `json:"user_id"`
	UserType     string `json:"user_type"`
	BusinessId   int    `json:"business_id"`
	OutletAccess string `json:"outlet_access"`
}

// read from header
func GetUserSessionFiber(c *fiber.Ctx) UserSession {
	var userSession UserSession
	userSession.UserId = cast.ToInt(c.Get("user_id"))
	userSession.UserType = c.Get("user_type")
	userSession.BusinessId = cast.ToInt(c.Get("business_id"))
	userSession.OutletAccess = c.Get("outlet_access")
	return userSession
}
