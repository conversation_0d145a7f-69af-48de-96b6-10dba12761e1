package domain

type Login struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type Token struct {
	Token   string
	Expired int64
	Type    string
}

type UserToken struct {
	User     User     `json:"user,omitempty"`
	Token    Token    `json:"token,omitempty"`
	UserRole UserRole `json:"user_role,omitempty"`
}

type LoginUseCase interface {
	Login(Login) (UserToken, error)
}

type LoginRepository interface {
	FindPassword(string) (string, error)
	FindUserByEmail(string) (User, error)
}
