package http

import (
	"fmt"
	"sort"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"gitlab.com/backend/api-hrm/domain"
)

// ScheduleHandler struct
type ScheduleHandler struct {
	ScheduleUseCase domain.ScheduleUseCase
}

// NewScheduleHandler func
// @Summary Initialize schedule routes
// @Description Set up schedule management endpoints
func NewScheduleHandler(app *fiber.App, uc domain.ScheduleUseCase) {
	handler := &ScheduleHandler{ScheduleUseCase: uc}
	v1 := app.Group("/v1")
	v1.Get("/schedules", handler.Fetch)
	v1.Get("/employee_schedule", handler.FetchEmployeeSchedule)
	v1.Get("/employee_shift", handler.FetchShiftEmployee)
	v1.Post("/create_header", handler.GetColFunc)
	v1.Post("/save_data_schedule", handler.SaveDataSchedule)
}

// Fetch get all schedule
// @Summary Get employee schedules
// @Description Retrieve employee schedules by outlet, month and year
// @Tags schedule
// @Accept json
// @Produce json
// @Param input body object true "Query params"
// @Success 200 {object} object{status=int,data=[]map[string]interface{}} "Schedule data with status"
// @Failure 500 {object} error "Internal server error"
// @Router /v1/schedules [get]
func (s *ScheduleHandler) Fetch(c *fiber.Ctx) error {
	// loc, _ := time.LoadLocation("Asia/Jakarta")
	type req struct {
		OutletID   int    `json:"outlet_id"`
		EmployeeID []int  `json:"employee_id"`
		Month      string `json:"month"`
		Year       string `json:"year"`
		StartDate  string `json:"start_date"`
	}

	var body req
	c.BodyParser(&body)
	startdate := body.Year + "-" + body.Month + "-" + body.StartDate
	schedule, err := s.ScheduleUseCase.Fetch(body.OutletID, body.EmployeeID, body.Month, body.Year, startdate)
	var group []map[string]interface{}
	for _, v := range schedule {
		arrDivide := cast.StructToMap(v)
		group = append(group, arrDivide)
	}

	mo := "2020-" + body.Month + "-25"
	m, _ := time.Parse("2006-01-02", mo)
	month := m.Month().String()
	data := cast.ArrayGroup(group, "employee_id")

	var filter []map[string]map[string]interface{}
	fill := make(map[string]map[string]interface{})
	var dateBreak []string
	var results []map[string]interface{}

	for _, b := range data {
		for i, f := range b {
			for _, k := range f {
				if k["outlet_id"] == 0 {
					k["outlet_id"] = body.OutletID
				}
				fill[strconv.Itoa(i.(int))] = map[string]interface{}{
					"outlet_id":     strconv.Itoa(k["outlet_id"].(int)),
					"outlet":        k["outlet_name"],
					"employee_id":   strconv.Itoa(k["employee_id"].(int)),
					"employee_name": k["employee_name"],
					"type_id":       strconv.Itoa(k["type_fkid"].(int)),
					"type":          k["type_name"],
					"bulan":         month,
				}

				dateSchedule := cast.ArrayGroup(f, "htc_date")

				for _, l := range dateSchedule {
					for _, m := range l {
						for _, n := range m {
							if n["htc_date"].(string) == n["schedule_date"].(string) && n["dtrc_status"].(int) == 1 {
								dateBreak = append(dateBreak, n["schedule_date"].(string))
							} else {
								dt := n["schedule_date"].(string)
								t, _ := time.Parse("2006-01-02", dt)
								// var day string
								d := t.Day()
								// if len(strconv.Itoa(d)) == 1 {
								// 	day = "0" + strconv.Itoa(d)
								// } else {
								// 	day = strconv.Itoa(d)
								// }
								fill[strconv.Itoa(i.(int))][strconv.Itoa(d)] = n["shift_id"]
								// fill[strconv.Itoa(i.(int))][day] = n["shift_id"]
							}
							if cast.StringInSlice(n["schedule_date"].(string), dateBreak) && n["dtrc_status"].(int) == 1 {
								dt := n["schedule_date"].(string)
								t, _ := time.Parse("2006-01-02", dt)
								// var day string
								d := t.Day()
								// if len(strconv.Itoa(d)) == 1 {
								// 	day = "0" + strconv.Itoa(d)
								// } else {
								// 	day = strconv.Itoa(d)
								// }
								fill[strconv.Itoa(i.(int))][strconv.Itoa(d)] = "OFF"
								// fill[strconv.Itoa(i.(int))][day] = "OFF"
							}
						}
					}
					filter = append(filter, fill)
					fill = map[string]map[string]interface{}{}
				}
				dateSchedule = []map[interface{}][]map[string]interface{}{}
				dateBreak = []string{}
				break
			}
		}
	}

	for _, v := range filter {
		for x := range v {
			results = append(results, v[x])
		}
	}

	// keys for sorting
	var keys []string
	for _, v := range results {
		keys = append(keys, v["employee_name"].(string))
	}
	sort.Strings(keys)
	var sortResults []map[string]interface{}
	for _, v := range keys {
		for _, w := range results {
			if w["employee_name"].(string) == v {
				sortResults = append(sortResults, w)
			}
		}
	}

	if err != nil {
		fmt.Println("error: ", err)
		return nil
	}
	return c.Status(200).JSON(&fiber.Map{
		"data":   sortResults,
		"status": 1,
	})
}

// FetchEmployeeSchedule handle func
// @Summary Get specific employee schedule
// @Description Get schedule details for a specific employee in an outlet
// @Tags schedule
// @Accept json
// @Produce json
// @Param input body object{outlet_id=int,employee_id=int} true "Query params"
// @Success 200 {array} domain.EmployeeSchedule "Employee schedule details"
// @Failure 500 {object} object{message=string,error=error} "Internal server error"
// @Router /v1/employee_schedule [get]
func (s *ScheduleHandler) FetchEmployeeSchedule(c *fiber.Ctx) error {
	type req struct {
		OutletID   int `json:"outlet_id"`
		EmployeeID int `json:"employee_id"`
	}

	var body req
	c.BodyParser(&body)
	employee, err := s.ScheduleUseCase.FetchEmployeeSchedule(body.OutletID, body.EmployeeID)
	if err != nil {
		fmt.Printf("fetching employee schedule error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "fetching employee schedule error",
			"error":   err,
		})
	}

	return c.JSON(employee)
}

// FetchShiftEmployee handle func
// @Summary Get employee shifts
// @Description Get shift details for employees in an outlet
// @Tags schedule
// @Accept json
// @Produce json
// @Param input body object{outlet_id=int} true "Query params"
// @Success 200 {array} interface{} "Shift details and colors"
// @Failure 500 {object} object{message=string,error=error} "Internal server error"
// @Router /v1/employee_shift [get]
func (s *ScheduleHandler) FetchShiftEmployee(c *fiber.Ctx) error {
	type req struct {
		OutletID int `json:"outlet_id"`
	}
	var body req
	c.BodyParser(&body)
	shifts, err := s.ScheduleUseCase.FetchShiftEmployee(body.OutletID)
	if err != nil {
		fmt.Printf("fetching shift employee error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "fetching shift employee error",
			"error":   err,
		})
	}

	var shift []interface{}
	shif := make(map[int][]string)
	shiftCol := make(map[string]string)

	for _, v := range shifts {
		shif[v.ShiftType] = append(shif[v.ShiftType], v.ShiftCode)
		shiftCol[v.ShiftCode] = v.ShiftColor
	}

	shift = append(shift, shif, shiftCol)
	return c.JSON(shift)
}

// GetColFunc handle func
// @Summary Get schedule header columns
// @Description Generate header columns for schedule display
// @Tags schedule
// @Accept json
// @Produce json
// @Param input body object{date=string,startDate=string,year=string,fullDate=string} true "Date parameters"
// @Success 200 {object} map[string]interface{} "Header column data"
// @Failure 500 {object} object{message=string,error=error} "Internal server error"
// @Router /v1/create_header [post]
func (s *ScheduleHandler) GetColFunc(c *fiber.Ctx) error {
	type req struct {
		Date      string `json:"date"`
		StartDate string `json:"startDate"`
		Year      string `json:"year"`
		FullDate  string `json:"fullDate"`
	}

	var body req
	c.BodyParser(&body)
	col, err := s.ScheduleUseCase.CreateHeader(body.FullDate)
	if err != nil {
		fmt.Printf("create header error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "create header error",
			"error":   err,
		})
	}
	return c.JSON(col)
}

// SaveDataSchedule handle func
// @Summary Save employee schedules
// @Description Save or update employee shift schedules
// @Tags schedule
// @Accept json
// @Produce json
// @Param input body object{data=[]object{employee_id=string,employee_name=string,outlet=string,outlet_id=string,type=string,type_id=string,bulan=string},month=string,year=string,outlet_id=string,date=string} true "Schedule data"
// @Success 200 {object} object{status=int,message=string} "Success response"
// @Failure 500 {object} object{status=int,message=string} "Error response"
// @Router /v1/save_data_schedule [post]
func (s *ScheduleHandler) SaveDataSchedule(c *fiber.Ctx) error {
	type DataShift struct {
		A            string `json:"1"`
		B            string `json:"2"`
		C            string `json:"3"`
		D            string `json:"4"`
		E            string `json:"5"`
		F            string `json:"6"`
		G            string `json:"7"`
		H            string `json:"8"`
		I            string `json:"9"`
		J            string `json:"10"`
		K            string `json:"11"`
		L            string `json:"12"`
		M            string `json:"13"`
		N            string `json:"14"`
		O            string `json:"15"`
		P            string `json:"16"`
		Q            string `json:"17"`
		R            string `json:"18"`
		S            string `json:"19"`
		T            string `json:"20"`
		U            string `json:"21"`
		V            string `json:"22"`
		W            string `json:"23"`
		X            string `json:"24"`
		Y            string `json:"25"`
		Z            string `json:"26"`
		AA           string `json:"27"`
		AB           string `json:"28"`
		AC           string `json:"29"`
		AD           string `json:"30"`
		AE           string `json:"31"`
		Bulan        string `json:"bulan"`
		EmplyeeID    string `json:"employee_id"`
		EmployeeName string `json:"employee_name"`
		Outlet       string `json:"outlet"`
		OutletID     string `json:"outlet_id"`
		Types        string `json:"type"`
		TypesID      string `json:"type_id"`
	}
	type req struct {
		Data     []DataShift `json:"data"`
		Month    string      `json:"month"`
		Year     string      `json:"year"`
		OutletID string      `json:"outlet_id"`
		Date     string      `json:"date"`
	}
	var body req
	c.BodyParser(&body)
	date := body.Year + "-" + body.Month + "-" + body.Date
	newDate, _ := time.Parse("2006-01-02", date)
	startDate := newDate.Format("2006-01-02")
	y, _ := strconv.Atoi(body.Year)
	countDays := cast.DaysIn(newDate.Month(), y)
	times := time.Hour * 24 * time.Duration(countDays-1)
	endDate := newDate.Add(times).Format("2006-01-02")
	dateRanges := cast.GetDateRanges(startDate, endDate)
	val := make([]map[string]interface{}, 0)
	mapStruct := make(map[string]interface{}, 0)

	for _, y := range body.Data {
		mapStruct = cast.StructToMap(y)
		val = append(val, mapStruct)
	}

	data := make([]map[string]interface{}, 0)
	dataValues := make(map[string]interface{}, 0)
	month := make([]string, 0)
	dateVal := make([]map[string]interface{}, 0)

	for _, v := range val {
		for w, x := range v {
			if w != "bulan" && w != "employee_id" && w != "employee_name" && w != "outlet" && w != "outlet_id" && w != "type" && w != "type_id" {
				if dateRanges[w] != nil {
					d, _ := time.Parse("2006-01-02", dateRanges[w].(string))
					dat := d.Format("2006-01")
					month = append(month, dat)
					dateVal = append(dateVal, map[string]interface{}{
						dateRanges[w].(string): x,
					})
				}
			}
			dataValues["bulan"] = v["bulan"]
			dataValues["employee_id"] = v["employee_id"]
			dataValues["employee_name"] = v["employee_name"]
			dataValues["outlet"] = v["outlet"]
			dataValues["outlet_id"] = v["outlet_id"]
			dataValues["type"] = v["type"]
			dataValues["type_id"] = v["type_id"]
			dataValues["date"] = dateVal
			dataValues["month"] = month
		}
		data = append(data, dataValues)
		dateVal = []map[string]interface{}{}
		month = []string{}
		dataValues = map[string]interface{}{}
	}

	err := s.ScheduleUseCase.SaveDataSchedule(data)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "Gagal menyimpan data",
			"status":  0,
		})
	}

	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"status":  1,
		"message": "Berhasil menyimpan data",
	})
}
