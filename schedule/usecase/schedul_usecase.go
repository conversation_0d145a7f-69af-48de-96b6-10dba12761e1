package usecase

import (
	"gitlab.com/backend/api-hrm/domain"
)

type scheduleUseCase struct {
	scheduleRepository domain.ScheduleRepository
}

// NewScheduleUseCase func
func NewScheduleUseCase(s domain.ScheduleRepository) domain.ScheduleUseCase {
	return &scheduleUseCase{scheduleRepository: s}
}

func (s *scheduleUseCase) Fetch(outletID int, employeeID []int, month string, year string, startDate string) ([]domain.DataSchedule, error) {
	allSchedule, err := s.scheduleRepository.Fetch(outletID, employeeID, month, year, startDate)

	if err != nil {
		return nil, err
	}
	return allSchedule, nil
}

func (s *scheduleUseCase) FetchEmployeeSchedule(outletID int, employeeID int) ([]domain.EmployeeSchedule, error) {
	employees, err := s.scheduleRepository.FetchEmployeeSchedule(outletID, employeeID)
	if err != nil {
		return nil, err
	}
	return employees, err
}

func (s *scheduleUseCase) FetchShiftEmployee(outletID int) ([]domain.ShiftEmployee, error) {
	shifts, err := s.scheduleRepository.FetchShiftEmployee(outletID)
	if err != nil {
		return nil, err
	}
	return shifts, err
}

func (s *scheduleUseCase) GetColumns(date string) ([]domain.Header, error) {
	columns, err := s.scheduleRepository.GetColumns(date)
	if err != nil {
		return nil, err
	}
	return columns, err
}

func (s *scheduleUseCase) CreateHeader(date string) (map[string]interface{}, error) {
	columns, err := s.scheduleRepository.CreateHeader(date)
	if err != nil {
		return nil, err
	}

	return columns, nil
}

func (s *scheduleUseCase) GetColFunc(date string) ([]interface{}, error) {
	return []interface{}{}, nil
}

func (s *scheduleUseCase) SaveDataSchedule(data []map[string]interface{}) error {
	return s.scheduleRepository.SaveDataSchedule(data)
}
