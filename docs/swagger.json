{"swagger": "2.0", "info": {"description": "API Documentation for UNIQ HRM System", "title": "UNIQ HRM API", "contact": {}, "version": "1.0"}, "host": "apis.uniqdev.web.id/hrm", "basePath": "/", "paths": {"/auth/login": {"post": {"description": "Authenticate user and return access token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Login to the system", "parameters": [{"description": "Login credentials", "name": "input", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.AuthInputLogin"}}], "responses": {"200": {"description": "Successfully authenticated with user details and token", "schema": {"$ref": "#/definitions/domain.UserToken"}}, "400": {"description": "Bad request - Invalid input", "schema": {}}, "401": {"description": "Unauthorized - Invalid email or password", "schema": {}}, "404": {"description": "User not found", "schema": {}}, "422": {"description": "Unprocessable entity", "schema": {}}}}}, "/cuti/v2": {"get": {"description": "Fetch cuti v2", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "Fetch cuti v2", "parameters": [{"type": "string", "description": "Bearer {token}", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Comma-separated list of outlet IDs", "name": "outlet_ids", "in": "query"}, {"type": "string", "description": "Comma-separated list of statuses (pending,approved,rejected)", "name": "status", "in": "query"}, {"type": "string", "description": "Start date in DD-MM-YYYY format", "name": "start_date", "in": "query"}, {"type": "string", "description": "End date in DD-MM-YYYY format", "name": "end_date", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/fiber.Map"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.CutiV2Detail"}}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Map"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fiber.Map"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v0/add_employee": {"post": {"description": "Add a new employee or update an existing one with optional additional information and attachments", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["employee"], "summary": "Add or update an employee (DEPRECATED)", "parameters": [{"type": "string", "description": "Employee Type ID (empty for new employee)", "name": "inputTypeId", "in": "formData"}, {"type": "string", "description": "Employee Name", "name": "inputEmmployeeName", "in": "formData", "required": true}, {"type": "string", "description": "NIK (16 digits)", "name": "inputNik", "in": "formData", "required": true}, {"type": "string", "description": "Employee Salary", "name": "inputSallary", "in": "formData", "required": true}, {"type": "string", "description": "Employee Type", "name": "inputEmployeeType", "in": "formData", "required": true}, {"type": "string", "description": "Maximum Leave Days", "name": "inputCuti", "in": "formData", "required": true}, {"type": "string", "description": "Full Name", "name": "name", "in": "formData", "required": true}, {"type": "string", "description": "Address", "name": "address", "in": "formData", "required": true}, {"type": "string", "description": "Email Address", "name": "email", "in": "formData", "required": true}, {"type": "string", "description": "Comma-separated list of outlet IDs that the employee has access to", "name": "outlet_ids", "in": "formData"}, {"type": "string", "description": "Phone Number", "name": "phone", "in": "formData", "required": true}, {"type": "string", "description": "Position/Job Title ID", "name": "jabatanId", "in": "formData", "required": true}, {"type": "string", "description": "Join Date (YYYY-MM-DD)", "name": "joinDate", "in": "formData", "required": true}, {"type": "string", "description": "NPWP (Tax ID)", "name": "npwp", "in": "formData"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "Additional Info Titles", "name": "inputTitle[]", "in": "formData"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "Additional Info IDs (for updates)", "name": "info_id[]", "in": "formData"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "Has Attachment Flags (true/false)", "name": "withFile[]", "in": "formData"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "Additional Info Contents", "name": "inputInfo[]", "in": "formData"}, {"type": "array", "items": {"type": "file"}, "collectionFormat": "csv", "description": "Attachments", "name": "inputAttachment[]", "in": "formData"}, {"type": "file", "description": "Employee Photo", "name": "photo", "in": "formData"}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "400": {"description": "Validation error", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/add_cuti": {"post": {"description": "Add a new cuti record with details and optional document attachments.\nCreate a new cuti request with details including dates, reason, and attachments", "consumes": ["multipart/form-data", "multipart/form-data"], "produces": ["application/json", "application/json"], "tags": ["cuti", "cuti"], "summary": "Add a new cuti request", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Outlet ID", "name": "inputOutletId", "in": "formData", "required": true}, {"type": "string", "description": "Employee ID", "name": "inputEmployeeId", "in": "formData", "required": true}, {"type": "string", "description": "Type of cuti", "name": "typeInput", "in": "formData", "required": true}, {"type": "string", "description": "Start date of cuti (YYYY-MM-DD)", "name": "inputStartDate", "in": "formData", "required": true}, {"type": "string", "description": "End date of cuti (YYYY-MM-DD)", "name": "inputEndDate", "in": "formData", "required": true}, {"type": "string", "description": "Description/Reason for cuti", "name": "inputDesc", "in": "formData", "required": true}, {"type": "file", "description": "Document attachments", "name": "inputDocument[]", "in": "formData"}, {"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "User type (employee/admin)", "name": "user_type", "in": "formData", "required": true}, {"type": "string", "description": "User ID", "name": "user_id", "in": "formData", "required": true}, {"type": "string", "description": "Outlet ID", "name": "inputOutletId", "in": "formData", "required": true}, {"type": "string", "description": "Employee ID", "name": "inputEmployeeId", "in": "formData", "required": true}, {"type": "string", "description": "Type of cuti (e.g., leave, sick, etc.)", "name": "typeInput", "in": "formData", "required": true}, {"type": "string", "description": "Start date of cuti (YYYY-MM-DD)", "name": "inputStartDate", "in": "formData", "required": true}, {"type": "string", "description": "End date of cuti (YYYY-MM-DD)", "name": "inputEndDate", "in": "formData", "required": true}, {"type": "string", "description": "Description of cuti", "name": "inputDesc", "in": "formData", "required": true}, {"type": "file", "description": "Document attachments", "name": "inputDocument[]", "in": "formData"}], "responses": {"200": {"description": "Success response with message and status", "schema": {"$ref": "#/definitions/fiber.Map"}}, "400": {"description": "Bad request or validation error", "schema": {"$ref": "#/definitions/fiber.Map"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v1/add_employee": {"post": {"description": "Add a new employee or update an existing one with optional additional information and attachments", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["employee"], "summary": "Add or update an employee", "parameters": [{"type": "string", "description": "Employee Type ID (empty for new employee)", "name": "inputTypeId", "in": "formData"}, {"type": "string", "description": "Employee Name", "name": "inputEmmployeeName", "in": "formData", "required": true}, {"type": "string", "description": "NIK (16 digits)", "name": "inputNik", "in": "formData", "required": true}, {"type": "string", "description": "Employee Salary", "name": "inputSallary", "in": "formData", "required": true}, {"type": "string", "description": "Employee Type", "name": "inputEmployeeType", "in": "formData", "required": true}, {"type": "string", "description": "Maximum Leave Days", "name": "inputCuti", "in": "formData", "required": true}, {"type": "string", "description": "Full Name", "name": "name", "in": "formData", "required": true}, {"type": "string", "description": "Address", "name": "address", "in": "formData", "required": true}, {"type": "string", "description": "Email Address", "name": "email", "in": "formData", "required": true}, {"type": "string", "description": "Comma-separated list of outlet IDs that the employee has access to", "name": "outlet_ids", "in": "formData"}, {"type": "string", "description": "Phone Number", "name": "phone", "in": "formData", "required": true}, {"type": "string", "description": "Position/Job Title ID", "name": "jabatanId", "in": "formData", "required": true}, {"type": "string", "description": "Join Date (YYYY-MM-DD)", "name": "joinDate", "in": "formData", "required": true}, {"type": "string", "description": "NPWP (Tax ID)", "name": "npwp", "in": "formData"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "Additional Info Titles", "name": "inputTitle[]", "in": "formData"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "Additional Info IDs (for updates)", "name": "info_id[]", "in": "formData"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "Has Attachment Flags (true/false)", "name": "withFile[]", "in": "formData"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "Additional Info Contents", "name": "inputInfo[]", "in": "formData"}, {"type": "array", "items": {"type": "file"}, "collectionFormat": "csv", "description": "Attachments", "name": "inputAttachment[]", "in": "formData"}, {"type": "file", "description": "Employee Photo", "name": "photo", "in": "formData"}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "400": {"description": "Validation error", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/all_type": {"get": {"description": "Get all employee types filtered by business ID from the header", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["type"], "summary": "Get all employee types with business ID", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Business ID", "name": "business_id", "in": "header", "required": true}], "responses": {"200": {"description": "List of employee types", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.HrmType"}}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/cancel_cuti": {"post": {"description": "<PERSON>cel an existing cuti record.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Cancel a cuti record (Web)", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"description": "Cuti cancellation details", "name": "request", "in": "body", "required": true, "schema": {"type": "object"}}, {"description": "Transaction Cuti ID", "name": "request.trc_id", "in": "body", "required": true, "schema": {"type": "integer"}}, {"description": "Employee ID", "name": "request.emp_id", "in": "body", "required": true, "schema": {"type": "integer"}}, {"description": "Outlet ID", "name": "request.outlet_id", "in": "body", "required": true, "schema": {"type": "integer"}}, {"description": "Start date of cuti (YYYY-MM-DD)", "name": "request.date_start", "in": "body", "required": true, "schema": {"type": "string"}}, {"description": "End date of cuti (YYYY-MM-DD)", "name": "request.date_end", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success response", "schema": {"$ref": "#/definitions/fiber.Map"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v1/create_header": {"post": {"description": "Generate header columns for schedule display", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["schedule"], "summary": "Get schedule header columns", "parameters": [{"description": "Date parameters", "name": "input", "in": "body", "required": true, "schema": {"type": "object", "properties": {"date": {"type": "string"}, "fullDate": {"type": "string"}, "startDate": {"type": "string"}, "year": {"type": "string"}}}}], "responses": {"200": {"description": "Header column data", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}}}}}}}, "/v1/cuti": {"get": {"description": "Get all cuti records based on the business ID from the header.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Get all cuti records based on the business ID from the header.", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.Cuti"}}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/delete_attach": {"post": {"description": "Delete an attachment from additional employee information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["employee"], "summary": "Delete an attachment", "parameters": [{"description": "Attachment ID", "name": "request", "in": "body", "required": true, "schema": {"type": "object"}}, {"description": "Attachment ID", "name": "request.id", "in": "body", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/delete_cuti": {"post": {"description": "Delete an existing cuti record and associated files.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Delete a cuti record (Web)", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"description": "Cuti deletion details", "name": "request", "in": "body", "required": true, "schema": {"type": "object"}}, {"description": "Transaction Cuti ID", "name": "request.trc_id", "in": "body", "required": true, "schema": {"type": "integer"}}, {"description": "Outlet ID", "name": "request.outlet_id", "in": "body", "required": true, "schema": {"type": "integer"}}, {"description": "Employee ID", "name": "request.emp_id", "in": "body", "required": true, "schema": {"type": "integer"}}, {"description": "Start date of cuti (YYYY-MM-DD)", "name": "request.start_date", "in": "body", "required": true, "schema": {"type": "string"}}, {"description": "End date of cuti (YYYY-MM-DD)", "name": "request.end_date", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success response", "schema": {"$ref": "#/definitions/fiber.Map"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v1/details_cuti/{id}": {"get": {"description": "Get detailed information about a specific cuti record, including attachments and records.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Get cuti details by ID", "parameters": [{"type": "integer", "description": "Cuti ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "Cuti details, attachments, and records", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v1/employee-import": {"post": {"security": [{"BearerAuth": []}], "description": "Import employee data from an uploaded Excel file. The Excel file should have specific column headers as defined in the API documentation.", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["employee"], "summary": "Import employees from Excel file", "parameters": [{"type": "file", "description": "Excel file containing employee data", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "Import results with success/error details", "schema": {"$ref": "#/definitions/domain.ExcelImportResponse"}}, "400": {"description": "Bad request - invalid file or format", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/employee/{id}": {"get": {"description": "Get detailed information about a specific employee including additional info. Supports fetching by hrm_employee_id or employee_id. When hrm_employee_id is 0 or not available, use employee_id query parameter.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["employee"], "summary": "Get a single employee by ID", "parameters": [{"type": "integer", "description": "HRM Employee ID (use 0 when fetching by employee_id)", "name": "id", "in": "path", "required": true}, {"type": "integer", "description": "Employee ID (required when path id is 0)", "name": "employee_id", "in": "query"}], "responses": {"200": {"description": "Employee details with additional info array", "schema": {"type": "object"}}, "400": {"description": "Bad request - missing required parameters", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/employee_schedule": {"get": {"description": "Get schedule details for a specific employee in an outlet", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["schedule"], "summary": "Get specific employee schedule", "parameters": [{"description": "Query params", "name": "input", "in": "body", "required": true, "schema": {"type": "object", "properties": {"employee_id": {"type": "integer"}, "outlet_id": {"type": "integer"}}}}], "responses": {"200": {"description": "Employee schedule details", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.EmployeeSchedule"}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}}}}}}}, "/v1/employee_shift": {"get": {"description": "Get shift details for employees in an outlet", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["schedule"], "summary": "Get employee shifts", "parameters": [{"description": "Query params", "name": "input", "in": "body", "required": true, "schema": {"type": "object", "properties": {"outlet_id": {"type": "integer"}}}}], "responses": {"200": {"description": "Shift details and colors", "schema": {"type": "array", "items": {}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}}}}}}}, "/v1/employees": {"get": {"description": "Get a list of all employees filtered by business ID from the header", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["employee"], "summary": "Get all employees", "parameters": [{"type": "string", "description": "Business ID", "name": "business_id", "in": "header", "required": true}], "responses": {"200": {"description": "List of employees", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.Employee"}}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/get_info/{id}": {"get": {"description": "Get all additional information records for a specific employee", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["employee"], "summary": "Get additional information for an employee", "parameters": [{"type": "integer", "description": "Employee ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Additional information records", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.HrmAddInfo"}}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/loan-types": {"get": {"security": [{"BearerAuth": []}], "description": "Get all loan types filtered by the authenticated user's business ID (admin_fkid)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["loans"], "summary": "Get all loan types", "responses": {"200": {"description": "List of loan types", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.LoanType"}}}, "401": {"description": "Unauthorized - invalid session", "schema": {"$ref": "#/definitions/fiber.Map"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v1/loans": {"post": {"security": [{"BearerAuth": []}], "description": "Create a new loan record with automatic loan type lookup/creation and total_due calculation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["loans"], "summary": "Create a new loan", "parameters": [{"description": "Loan information", "name": "loan", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.LoanRequest"}}], "responses": {"201": {"description": "Successfully created loan with loan ID", "schema": {"$ref": "#/definitions/fiber.Map"}}, "400": {"description": "Bad request - validation error", "schema": {"$ref": "#/definitions/fiber.Map"}}, "401": {"description": "Unauthorized - invalid session", "schema": {"$ref": "#/definitions/fiber.Map"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v1/presensi": {"get": {"description": "Retrieve all employee attendance records including late status, overtime, and break times", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["presensi"], "summary": "Get all attendance records", "responses": {"200": {"description": "List of attendance records", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.Presensi"}}}, "500": {"description": "Internal server error", "schema": {}}}}}, "/v1/presensi/import": {"post": {"security": [{"BearerAuth": []}], "description": "Import presensi data from Excel file with outlet ID and overwrite option", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["presensi"], "summary": "Import presensi from Excel file", "parameters": [{"type": "string", "description": "Outlet ID", "name": "outlet_id", "in": "formData", "required": true}, {"type": "string", "description": "Overwrite existing data (true/false)", "name": "overwrite", "in": "formData"}, {"type": "file", "description": "Excel file with presensi data", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "Success response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "integer"}}}}, "400": {"description": "Bad request", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "integer"}}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}, "status": {"type": "integer"}}}}}}}, "/v1/presensi/template": {"get": {"security": [{"BearerAuth": []}], "description": "Download an Excel template file with the correct format for importing attendance data", "consumes": ["application/json"], "produces": ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"], "tags": ["presensi"], "summary": "Download Excel template for presensi import", "responses": {"200": {"description": "Excel template file", "schema": {"type": "file"}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}, "status": {"type": "integer"}}}}}}}, "/v1/save_data_schedule": {"post": {"description": "Save or update employee shift schedules", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["schedule"], "summary": "Save employee schedules", "parameters": [{"description": "Schedule data", "name": "input", "in": "body", "required": true, "schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"bulan": {"type": "string"}, "employee_id": {"type": "string"}, "employee_name": {"type": "string"}, "outlet": {"type": "string"}, "outlet_id": {"type": "string"}, "type": {"type": "string"}, "type_id": {"type": "string"}}}}, "date": {"type": "string"}, "month": {"type": "string"}, "outlet_id": {"type": "string"}, "year": {"type": "string"}}}}], "responses": {"200": {"description": "Success response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "integer"}}}}, "500": {"description": "Error response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "integer"}}}}}}}, "/v1/save_details_cuti": {"post": {"description": "Save or update details of a cuti record, including status, reason, and attachments.", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Save/Update cuti details (Web)", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Input Type Admin", "name": "inputTypeAdm", "in": "formData", "required": true}, {"type": "string", "description": "Input User ID", "name": "inputUserID", "in": "formData", "required": true}, {"type": "string", "description": "Input Admin ID", "name": "inputAdmin", "in": "formData", "required": true}, {"type": "string", "description": "Input Admin Email", "name": "inputEmailAdm", "in": "formData", "required": true}, {"type": "string", "description": "NIK", "name": "nik", "in": "formData", "required": true}, {"type": "string", "description": "Transaction Cuti ID", "name": "inputTransCutiId", "in": "formData", "required": true}, {"type": "string", "description": "Employee ID", "name": "emp_id", "in": "formData", "required": true}, {"type": "string", "description": "Outlet ID", "name": "outlet_id", "in": "formData", "required": true}, {"type": "string", "description": "Employee Name", "name": "emp_name", "in": "formData", "required": true}, {"type": "string", "description": "Employee Email", "name": "email", "in": "formData", "required": true}, {"type": "file", "description": "Additional document attachments", "name": "addDocument[]", "in": "formData"}], "responses": {"200": {"description": "Success response", "schema": {"$ref": "#/definitions/fiber.Map"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v1/schedules": {"get": {"description": "Retrieve employee schedules by outlet, month and year", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["schedule"], "summary": "Get employee schedules", "parameters": [{"description": "Query params", "name": "input", "in": "body", "required": true, "schema": {"type": "object"}}], "responses": {"200": {"description": "Schedule data with status", "schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "additionalProperties": true}}, "status": {"type": "integer"}}}}, "500": {"description": "Internal server error", "schema": {}}}}}, "/v1/shifts": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve all shifts for a business with optional filtering by outlet IDs. When outlet_ids parameter is provided, only shifts for those specific outlets will be returned. Multiple outlet IDs can be specified as comma-separated values.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["shift"], "summary": "Get all shifts", "parameters": [{"type": "string", "description": "Business ID", "name": "business_id", "in": "header", "required": true}, {"type": "string", "description": "Comma-separated outlet IDs for filtering (e.g., 45,67,4). If not provided, all shifts for the business will be returned.", "name": "outlet_ids", "in": "query"}], "responses": {"200": {"description": "List of shifts matching the filter criteria", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.MasterShift"}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}, "status": {"type": "integer"}}}}}}}, "/v1/shifts/add": {"post": {"security": [{"BearerAuth": []}], "description": "Create a new shift or update existing one", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["shift"], "summary": "Add or update shift", "parameters": [{"description": "Shift details", "name": "input", "in": "body", "required": true, "schema": {"type": "object", "properties": {"shift_active": {"type": "string"}, "shift_code": {"type": "string"}, "shift_color": {"type": "string"}, "shift_fkid": {"type": "string"}, "shift_id": {"type": "integer"}, "shift_in": {"type": "string"}, "shift_office": {"type": "array", "items": {"type": "string"}}, "shift_out": {"type": "string"}, "shift_tolerance": {"type": "string"}, "shift_type": {"type": "integer"}}}}], "responses": {"200": {"description": "Success response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "integer"}}}}, "400": {"description": "Validation error", "schema": {"type": "object", "properties": {"message": {"type": "object", "additionalProperties": {"type": "string"}}, "status": {"type": "integer"}}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}, "status": {"type": "integer"}}}}}}}, "/v1/shifts/delete": {"post": {"security": [{"BearerAuth": []}], "description": "Delete an existing shift", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["shift"], "summary": "Delete shift", "parameters": [{"description": "Shift to delete", "name": "input", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ShiftAdd"}}], "responses": {"200": {"description": "Success response", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}, "status": {"type": "integer"}}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}, "status": {"type": "integer"}}}}}}}, "/v1/shifts/export": {"get": {"security": [{"BearerAuth": []}], "description": "Export all shifts for a business to Excel file", "consumes": ["application/json"], "produces": ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"], "tags": ["shift"], "summary": "Export shifts to Excel", "responses": {"200": {"description": "Excel file with shift data", "schema": {"type": "file"}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}, "status": {"type": "integer"}}}}}}}, "/v1/shifts/group": {"post": {"security": [{"BearerAuth": []}], "description": "Retrieve shift groups by outlet IDs", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["shift"], "summary": "Get shift groups", "parameters": [{"description": "Outlet IDs", "name": "input", "in": "body", "required": true, "schema": {"type": "object", "properties": {"outlet_id": {"type": "array", "items": {}}}}}], "responses": {"200": {"description": "List of shift groups", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.ShiftGroup"}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}}}}}}}, "/v1/shifts/import": {"post": {"security": [{"BearerAuth": []}], "description": "Import shifts from Excel file with outlet IDs", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["shift"], "summary": "Import shifts from Excel file", "parameters": [{"type": "string", "description": "Comma-separated outlet IDs", "name": "outlet_ids", "in": "formData", "required": true}, {"type": "file", "description": "Excel file with shift data", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "Success response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "integer"}}}}, "400": {"description": "Bad request", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "integer"}}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}, "status": {"type": "integer"}}}}}}}, "/v1/shifts/template": {"get": {"security": [{"BearerAuth": []}], "description": "Download an Excel template file with the correct format for importing shifts", "consumes": ["application/json"], "produces": ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"], "tags": ["shift"], "summary": "Download Excel template for shift import", "responses": {"200": {"description": "Excel template file", "schema": {"type": "file"}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}, "status": {"type": "integer"}}}}}}}, "/v1/shifts/update_hot": {"post": {"security": [{"BearerAuth": []}], "description": "Update multiple shifts at once", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["shift"], "summary": "Bulk update shifts", "parameters": [{"description": "Collection of shifts to update", "name": "input", "in": "body", "required": true, "schema": {"type": "object", "properties": {"collection": {"type": "array", "items": {"type": "object", "additionalProperties": true}}}}}], "responses": {"200": {"description": "Success response", "schema": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "integer"}}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "properties": {"error": {}, "message": {"type": "string"}, "status": {"type": "integer"}}}}}}}, "/v1/type/add": {"post": {"description": "Create a new employee type with validation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["type"], "summary": "Add a new employee type", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "User ID", "name": "user_id", "in": "header", "required": true}, {"description": "Employee type information", "name": "type", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.HrmMasterType"}}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "400": {"description": "Validation error", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/type/delete": {"post": {"description": "Delete an existing employee type by ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["type"], "summary": "Delete an employee type", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"description": "Employee type to delete (only type_id is required)", "name": "type", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.HrmMasterType"}}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/type/update": {"post": {"description": "Update an employee type with validation", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["type"], "summary": "Update an existing employee type", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "User ID", "name": "user_id", "in": "header", "required": true}, {"description": "Updated employee type information", "name": "type", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.HrmMasterType"}}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "400": {"description": "Validation error", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/type/{id}": {"get": {"description": "Get a specific employee type by its ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["type"], "summary": "Get employee type by ID", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "integer", "description": "Type ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Employee type details", "schema": {"$ref": "#/definitions/domain.HrmMasterType"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v1/types": {"get": {"description": "Get all employee types without filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["type"], "summary": "Get all employee types", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "List of all employee types", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.HrmMasterType"}}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v2/add_cuti": {"post": {"description": "Add a new cuti record with details and optional document attachments from mobile app", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Add a new cuti record (Mobile)", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Employee ID", "name": "hrm_employee_id", "in": "formData", "required": true}, {"type": "string", "description": "Outlet ID(s) (comma separated)", "name": "outlet_fkid", "in": "formData", "required": true}, {"type": "string", "description": "Creation timestamp", "name": "trc_created", "in": "formData", "required": true}, {"type": "string", "description": "Start date of cuti (YYYY-MM-DD)", "name": "trc_date_start", "in": "formData", "required": true}, {"type": "string", "description": "End date of cuti (YYYY-MM-DD)", "name": "trc_date_end", "in": "formData", "required": true}, {"type": "string", "description": "Reason for cuti", "name": "trc_reason", "in": "formData", "required": true}, {"type": "string", "description": "Status of cuti", "name": "trc_status", "in": "formData", "required": true}, {"type": "string", "description": "Type of cuti", "name": "type", "in": "formData", "required": true}, {"type": "file", "description": "Document attachments", "name": "document", "in": "formData"}, {"type": "string", "description": "Transaction Cuti ID (for updates)", "name": "trc_id", "in": "formData"}], "responses": {"200": {"description": "Success response with message and status", "schema": {"$ref": "#/definitions/fiber.Map"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v2/add_device": {"post": {"description": "Register a new device with its token for push notifications", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["messaging"], "summary": "Register a device for notifications", "parameters": [{"description": "Device information", "name": "device", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.DeviceCreateRequest"}}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "400": {"description": "Bad request error", "schema": {"type": "object"}}, "401": {"description": "Unauthorized error", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v2/add_message": {"post": {"description": "Create a new notification message for a user", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["messaging"], "summary": "Add a new notification message", "parameters": [{"type": "string", "description": "Message title", "name": "title", "in": "formData", "required": true}, {"type": "string", "description": "Message content", "name": "message", "in": "formData", "required": true}, {"type": "string", "description": "Read status (0/1)", "name": "is_read", "in": "formData", "required": true}, {"type": "string", "description": "Viewed status (0/1)", "name": "is_viewed", "in": "formData", "required": true}, {"type": "string", "description": "Creation timestamp", "name": "data_created", "in": "formData", "required": true}, {"type": "string", "description": "Message type", "name": "type", "in": "formData", "required": true}, {"type": "string", "description": "Receiver ID", "name": "receiver_id", "in": "formData", "required": true}, {"type": "string", "description": "Admin ID", "name": "admin_fkid", "in": "formData", "required": true}, {"type": "string", "description": "Notification type", "name": "notification_type", "in": "formData", "required": true}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v2/attendance/{outlet_id}/{date}/{emp_id}": {"get": {"description": "Get attendance information for a specific employee at a given outlet on a specific date", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Get employee attendance by outlet, date and employee ID (Mobile)", "parameters": [{"type": "integer", "description": "Outlet ID", "name": "outlet_id", "in": "path", "required": true}, {"type": "string", "description": "Date (YYYY-MM-DD)", "name": "date", "in": "path", "required": true}, {"type": "integer", "description": "Employee ID", "name": "emp_id", "in": "path", "required": true}], "responses": {"200": {"description": "Attendance information", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v2/change_password": {"post": {"description": "Change an employee's password with verification of old password", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["employee"], "summary": "Change employee password", "parameters": [{"type": "string", "description": "Employee email", "name": "email", "in": "formData", "required": true}, {"type": "string", "description": "Old password", "name": "old_pass", "in": "formData", "required": true}, {"type": "string", "description": "New password", "name": "new_pass", "in": "formData", "required": true}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "400": {"description": "Bad request or validation error", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v2/cuti_time_details/{emp_id}": {"get": {"description": "Get detailed information about cuti records for a specific employee.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Get employee cuti details by employee ID (Mobile)", "parameters": [{"type": "integer", "description": "Employee ID", "name": "emp_id", "in": "path", "required": true}], "responses": {"200": {"description": "Employee cuti details", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v2/delete_messages": {"post": {"description": "Delete multiple messages by their IDs", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["messaging"], "summary": "Delete multiple messages", "parameters": [{"type": "string", "description": "Comma-separated list of message IDs", "name": "messages_id", "in": "formData", "required": true}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v2/delete_notif": {"post": {"description": "Delete a specific notification by ID", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["messaging"], "summary": "Delete a notification", "parameters": [{"type": "string", "description": "Notification ID", "name": "notif_id", "in": "formData", "required": true}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v2/emp_role/{emp_id}": {"get": {"description": "Get the role information for a specific employee", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Get employee role by employee ID (Mobile)", "parameters": [{"type": "integer", "description": "Employee ID", "name": "emp_id", "in": "path", "required": true}], "responses": {"200": {"description": "Employee role information", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v2/employee": {"get": {"description": "Get detailed employee data including job position (jabatan), tenure calculation, employee type and flattened additional information. Returns position name from employees_jabatan table and tenure calculated from date_join. Supports filtering by outlet IDs, employee type IDs, jabatan IDs, and HR registration status.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["employee"], "summary": "Get detailed employee data with flattened additional info", "parameters": [{"type": "string", "description": "Comma-separated outlet IDs for filtering (e.g., 45,67,4)", "name": "outlet_ids", "in": "query"}, {"type": "string", "description": "Comma-separated employee type IDs for filtering (e.g., 1,2,3)", "name": "employee_type_ids", "in": "query"}, {"type": "string", "description": "Comma-separated jabatan IDs for filtering (e.g., 1,2,3)", "name": "jabatan_ids", "in": "query"}, {"type": "string", "description": "Filter by HR registration status: 'all' for all employees, 'hr_only' for HR registered only (default: hr_only)", "name": "hr_registered", "in": "query"}], "responses": {"200": {"description": "List of employees with position, tenure, and flattened additional info", "schema": {"type": "array", "items": {"type": "object"}}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v2/fetch_cuti_details/{trc_id}": {"get": {"description": "Get detailed information about a specific cuti transaction", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Get cuti details by transaction ID (Mobile)", "parameters": [{"type": "integer", "description": "Transaction Cuti ID", "name": "trc_id", "in": "path", "required": true}], "responses": {"200": {"description": "Cuti details", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v2/fetch_device/{token}": {"get": {"description": "Get device information by FCM token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["messaging"], "summary": "Get user device by token", "parameters": [{"type": "string", "description": "FCM token", "name": "token", "in": "path", "required": true}], "responses": {"200": {"description": "Device information", "schema": {"$ref": "#/definitions/domain.Device"}}, "400": {"description": "Bad request error", "schema": {"type": "object"}}}}}, "/v2/fetch_employee_details/{emp_id}": {"get": {"description": "Get detailed information about a specific employee", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Get employee details by employee ID (Mobile)", "parameters": [{"type": "integer", "description": "Employee ID", "name": "emp_id", "in": "path", "required": true}], "responses": {"200": {"description": "Employee details", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v2/fetch_employee_type/{emp_id}": {"get": {"description": "Get the type information for a specific employee", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Get employee type by employee ID (Mobile)", "parameters": [{"type": "integer", "description": "Employee ID", "name": "emp_id", "in": "path", "required": true}], "responses": {"200": {"description": "Employee type information", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v2/fetch_messages": {"get": {"description": "Get all messages for a specific user filtered by type", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["messaging"], "summary": "Get user messages", "responses": {"200": {"description": "List of messages", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.Message"}}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v2/fetch_outlet_latlng/{outlet_id}": {"get": {"description": "Get latitude and longitude information for one or more outlets", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Get outlet latitude and longitude (Mobile)", "parameters": [{"type": "string", "description": "Outlet ID(s) (comma separated)", "name": "outlet_id", "in": "path", "required": true}], "responses": {"200": {"description": "Outlet location information", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v2/fetch_single_cuti_details/{trc_id}": {"get": {"description": "Get detailed information about a specific cuti record for an employee", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Get single employee cuti by transaction ID (Mobile)", "parameters": [{"type": "integer", "description": "Transaction Cuti ID", "name": "trc_id", "in": "path", "required": true}], "responses": {"200": {"description": "Employee cuti information", "schema": {"type": "object"}}, "400": {"description": "Bad request error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v2/get_vector/{emp_id}": {"get": {"description": "Get the vector representation of an employee's profile image", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["employee"], "summary": "Get employee image vector", "parameters": [{"type": "integer", "description": "Employee ID", "name": "emp_id", "in": "path", "required": true}], "responses": {"200": {"description": "Image vector data", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.VectorImg"}}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v2/scan_attendance": {"post": {"description": "Record attendance for an employee with details like outlet, time, and code", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Record employee attendance (Mobile)", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Outlet ID", "name": "tip_outlet_id", "in": "formData", "required": true}, {"type": "string", "description": "Employee name", "name": "tip_nama_karyawan", "in": "formData", "required": true}, {"type": "string", "description": "Date (YYYY-MM-DD)", "name": "tip_tanggal", "in": "formData", "required": true}, {"type": "string", "description": "Time (HH:MM:SS)", "name": "tip_jam", "in": "formData", "required": true}, {"type": "string", "description": "Attendance code", "name": "tip_kode", "in": "formData", "required": true}, {"type": "string", "description": "Employee ID", "name": "employee_id", "in": "formData", "required": true}], "responses": {"200": {"description": "Success response with message and status", "schema": {"$ref": "#/definitions/fiber.Map"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v2/shift_time/{outlet_id}/{emp_id}": {"get": {"description": "Get the shift time for a specific employee at a given outlet for the current date.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cuti"], "summary": "Get employee shift time by outlet and employee ID (Mobile)", "parameters": [{"type": "integer", "description": "Outlet ID", "name": "outlet_id", "in": "path", "required": true}, {"type": "integer", "description": "Employee ID", "name": "emp_id", "in": "path", "required": true}], "responses": {"200": {"description": "Shift time data", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.ShiftTime"}}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/fiber.Map"}}}}}, "/v2/update_message_read": {"post": {"description": "Update the read status of multiple messages", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["messaging"], "summary": "Mark messages as read", "parameters": [{"type": "string", "description": "Comma-separated list of notification IDs", "name": "where_id", "in": "formData", "required": true}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v2/update_message_view": {"post": {"description": "Update the viewed status of multiple messages", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["messaging"], "summary": "Mark messages as viewed", "parameters": [{"type": "string", "description": "Comma-separated list of notification IDs", "name": "where_id", "in": "formData", "required": true}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v2/update_profile_photo": {"post": {"description": "Update an employee's profile photo and generate vector representation", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["employee"], "summary": "Update employee profile photo", "parameters": [{"type": "string", "description": "Employee ID", "name": "emp_id", "in": "formData", "required": true}, {"type": "file", "description": "Profile photo", "name": "photo", "in": "formData", "required": true}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}, "/v2/update_user_device": {"post": {"description": "Update device information for a specific user", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["messaging"], "summary": "Update user device information", "parameters": [{"type": "string", "description": "User ID", "name": "user_id", "in": "formData", "required": true}, {"type": "string", "description": "Device type (android/ios)", "name": "device", "in": "formData", "required": true}, {"type": "string", "description": "Device record ID", "name": "id", "in": "formData", "required": true}], "responses": {"200": {"description": "Success response with status", "schema": {"type": "object"}}, "400": {"description": "Bad request error", "schema": {"type": "object"}}}}}, "/v2/user_devices/{emp_id}": {"get": {"description": "Get all registered devices for a specific user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["messaging"], "summary": "Get user devices", "parameters": [{"type": "integer", "description": "Employee ID", "name": "emp_id", "in": "path", "required": true}], "responses": {"200": {"description": "List of user devices", "schema": {"type": "array", "items": {"$ref": "#/definitions/domain.Device"}}}, "500": {"description": "Internal server error", "schema": {"type": "object"}}}}}}, "definitions": {"domain.AuthInputLogin": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string"}, "password": {"type": "string"}}}, "domain.Cuti": {"type": "object", "properties": {"hrm_employee_id": {"type": "string"}, "outlet_fkid": {"type": "string"}, "trc_created": {"type": "string"}, "trc_date_end": {"type": "string"}, "trc_date_start": {"type": "string"}, "trc_id": {"type": "string"}, "trc_reason": {"type": "string"}, "trc_status": {"type": "string"}, "trc_update": {"type": "string"}, "type": {"type": "string"}}}, "domain.CutiV2Detail": {"type": "object", "properties": {"action": {"type": "integer"}, "address": {"type": "string"}, "created": {"type": "string"}, "descr": {"type": "string"}, "employee_id": {"type": "integer"}, "employee_name": {"type": "string"}, "end_date": {"type": "string"}, "nik": {"type": "string"}, "no": {"type": "integer"}, "outlet": {"type": "string"}, "outlet_fkid": {"type": "integer"}, "phone": {"type": "string"}, "reason": {"type": "string"}, "start_date": {"type": "string"}, "status": {"type": "string"}, "trc_id": {"type": "integer"}}}, "domain.Device": {"type": "object", "properties": {"device": {"type": "string"}, "device_info": {"type": "string"}, "id": {"type": "integer"}, "token": {"type": "string"}, "user_id": {"type": "string"}, "user_type": {"type": "string"}}}, "domain.DeviceCreateRequest": {"type": "object", "required": ["device", "device_info", "token"], "properties": {"device": {"type": "string"}, "device_info": {"description": "JSON format device information", "type": "string"}, "token": {"type": "string"}}}, "domain.Employee": {"type": "object", "properties": {"EMP_ID": {"type": "integer"}, "HEMP_FKID": {"type": "integer"}, "HEMP_ID": {"type": "integer"}, "ID": {"type": "integer"}, "MAX_LEAVE": {"type": "integer"}, "NAME": {"type": "string"}, "NIK": {"type": "string"}, "NO": {"type": "integer"}, "SALLARY": {"type": "integer"}, "TYPE_FKID": {"type": "integer"}, "TYPE_NAME": {"type": "string"}, "admin_fkid": {"type": "integer"}, "data_created": {"type": "string"}, "data_modified": {"type": "string"}, "data_status": {"type": "string"}, "date_join": {"type": "integer"}, "jabatan_fkid": {"type": "integer"}, "jabatan_name": {"type": "string"}}}, "domain.EmployeeSchedule": {"type": "object", "properties": {"employee_id": {"type": "integer"}, "name": {"type": "string"}, "nik": {"type": "string"}, "outlet": {"type": "string"}}}, "domain.ExcelImportError": {"type": "object", "properties": {"column": {"type": "string"}, "field": {"type": "string"}, "message": {"type": "string"}, "row": {"type": "integer"}, "value": {"type": "string"}}}, "domain.ExcelImportResponse": {"type": "object", "properties": {"error_count": {"type": "integer"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/domain.ExcelImportError"}}, "import_summary": {"$ref": "#/definitions/domain.ExcelImportSummary"}, "message": {"type": "string"}, "processed_rows": {"type": "integer"}, "success": {"type": "boolean"}, "success_count": {"type": "integer"}}}, "domain.ExcelImportSummary": {"type": "object", "properties": {"existing_accounts": {"type": "integer"}, "new_accounts": {"type": "integer"}, "new_employees": {"type": "integer"}, "updated_employees": {"type": "integer"}}}, "domain.HrmAddInfo": {"type": "object", "properties": {"add_id": {"type": "integer"}, "attachment": {"type": "string"}, "hrm_employee_fkid": {"type": "integer"}, "info": {"type": "string"}, "title": {"type": "string"}}}, "domain.HrmMasterType": {"type": "object", "properties": {"admin_fkid": {"type": "integer"}, "rest_hours": {"type": "integer"}, "rest_minutes": {"type": "integer"}, "type_hours": {"type": "integer"}, "type_id": {"type": "integer"}, "type_name": {"type": "string"}}}, "domain.HrmType": {"type": "object", "properties": {"ACTION": {"type": "integer"}, "NO": {"type": "integer"}, "REST_HOURS": {"type": "integer"}, "TYPE_HOURS": {"type": "integer"}, "TYPE_NAME": {"type": "string"}, "admin_fkid": {"type": "integer"}}}, "domain.LoanRequest": {"type": "object", "required": ["first_due_date", "hrm_employee_fkid", "loan_type_name", "principal", "term_months"], "properties": {"first_due_date": {"type": "string"}, "hrm_employee_fkid": {"type": "integer"}, "interest_rate": {"type": "number", "minimum": 0}, "loan_type_name": {"type": "string"}, "notes": {"type": "string"}, "principal": {"type": "number"}, "term_months": {"type": "integer"}}}, "domain.LoanType": {"type": "object", "properties": {"admin_fkid": {"type": "integer"}, "created_at": {"type": "integer"}, "interest_rate": {"type": "number"}, "loan_type_id": {"type": "integer"}, "loan_type_name": {"type": "string"}, "principal": {"type": "number"}, "term_months": {"type": "integer"}, "updated_at": {"type": "integer"}}}, "domain.MasterShift": {"type": "object", "properties": {"shift_active": {"type": "integer"}, "shift_code": {"type": "string"}, "shift_color": {"type": "string"}, "shift_fkid": {"type": "integer"}, "shift_id": {"type": "integer"}, "shift_id_name": {"type": "integer"}, "shift_in": {"type": "string"}, "shift_max_in": {"type": "string"}, "shift_name": {"type": "string"}, "shift_name_id": {"type": "string"}, "shift_office": {"type": "string"}, "shift_office_id": {"type": "integer"}, "shift_out": {"type": "string"}, "shift_tolerance": {"type": "integer"}, "shift_type": {"type": "string"}, "shift_type_id": {"type": "integer"}}}, "domain.Message": {"type": "object", "properties": {"admin_fkid": {"type": "integer"}, "data_created": {"type": "integer"}, "is_read": {"type": "integer"}, "is_viewed": {"type": "integer"}, "message": {"type": "string"}, "notification_data": {"type": "string"}, "notification_id": {"type": "integer"}, "notification_type": {"type": "string"}, "receiver_id": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string"}}}, "domain.Presensi": {"type": "object", "properties": {"conf_late_break": {"type": "string"}, "employee_id": {"type": "string"}, "late_break_in": {"type": "string"}, "overtime": {"type": "string"}, "promise": {"type": "string"}, "tip_confirm_terlambat": {"type": "string"}, "tip_hash": {"type": "string"}, "tip_id": {"type": "string"}, "tip_is_terlambat": {"type": "string"}, "tip_jam": {"type": "string"}, "tip_kode": {"type": "string"}, "tip_name_karyawan": {"type": "string"}, "tip_nik": {"type": "string"}, "tip_outlet_id": {"type": "string"}, "tip_tanggal": {"type": "string"}, "tipe_terlambat": {"type": "string"}}}, "domain.ShiftAdd": {"type": "object", "properties": {"shift_active": {"type": "integer"}, "shift_code": {"type": "string"}, "shift_color": {"type": "string"}, "shift_fkid": {"type": "integer"}, "shift_id": {"type": "integer"}, "shift_in": {"type": "string"}, "shift_office": {"type": "string"}, "shift_out": {"type": "string"}, "shift_tolerance": {"type": "integer"}, "shift_type": {"type": "string"}}}, "domain.ShiftGroup": {"type": "object", "properties": {"id": {"type": "integer"}, "outlet_fkid": {"type": "integer"}, "shift_fkid": {"type": "integer"}, "shift_name": {"type": "string"}}}, "domain.ShiftTime": {"type": "object", "properties": {"rest_hours": {"type": "integer"}, "shift_in": {"type": "string"}, "shift_out": {"type": "string"}, "shift_tolerance": {"type": "integer"}, "shift_type": {"type": "integer"}, "type_hours": {"type": "integer"}, "type_name": {"type": "string"}}}, "domain.Token": {"type": "object", "properties": {"expired": {"type": "integer", "format": "int64"}, "token": {"type": "string"}, "type": {"type": "string"}}}, "domain.User": {"type": "object", "properties": {"business_id": {"type": "string"}, "email": {"type": "string"}, "name": {"type": "string"}, "phone": {"type": "string"}, "user_id": {"type": "string"}, "user_type": {"type": "string"}}}, "domain.UserRole": {"type": "object", "properties": {"outletAccess": {"type": "string"}}}, "domain.UserToken": {"type": "object", "properties": {"token": {"$ref": "#/definitions/domain.Token"}, "user": {"$ref": "#/definitions/domain.User"}, "user_role": {"$ref": "#/definitions/domain.UserRole"}}}, "domain.VectorImg": {"type": "object", "properties": {"img_vector": {"type": "string"}}}, "fiber.Map": {"type": "object", "additionalProperties": true}}, "securityDefinitions": {"BearerAuth": {"description": "Enter the token with the 'Bearer ' prefix, e.g. 'Bearer abcde12345'", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}